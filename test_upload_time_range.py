#!/usr/bin/env python3
"""
Test script for the new upload time range columns
"""

from grib_processor import process_single_grib_file, process_grib_files_for_run
import os
import logging
import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_upload_time_range():
    """Test the new upload time range functionality"""

    print("🔍 Testing Upload Time Range Columns")
    print("=" * 50)

    # Test 1: Single file processing
    print("📁 Test 1: Single File Processing")
    print("-" * 30)

    # Find a test file
    if os.path.exists("Archive"):
        files = [f for f in os.listdir("Archive") if not f.endswith('.idx')]
        if files:
            test_file = os.path.join("Archive", files[0])
            print(f"Testing with file: {test_file}")

            # Get file modification time directly
            mod_time = os.path.getmtime(test_file)
            expected_hour = datetime.datetime.fromtimestamp(mod_time).hour
            print(f"Expected upload hour: {expected_hour}")

            # Process the file
            df, csv_file, validation = process_single_grib_file(test_file)

            if not df.empty:
                if 'min_date_upload' in df.columns and 'max_date_upload' in df.columns:
                    min_date = df['min_date_upload'].iloc[0]
                    max_date = df['max_date_upload'].iloc[0]
                    print(f"✅ Upload date columns found!")
                    print(f"Min upload date: {min_date}")
                    print(f"Max upload date: {max_date}")
                    print(f"Expected hour: {expected_hour}")
                    print(f"Match: {min_date.hour == expected_hour and max_date.hour == expected_hour}")
                else:
                    print("❌ Upload date columns not found")
                    print(f"Available columns: {list(df.columns)}")
            else:
                print("❌ No data processed")
        else:
            print("❌ No files found in Archive")
    else:
        print("❌ Archive folder not found")

    # Test 2: Batch processing
    print(f"\n📊 Test 2: Batch Processing")
    print("-" * 30)

    run_date = "2025-04-28-00"
    print(f"Testing with run date: {run_date}")

    # Get file modification times manually for comparison
    if os.path.exists("Archive"):
        files = [f for f in os.listdir("Archive") if f.startswith('A1D0428') and not f.endswith('.idx')]
        if files:
            upload_hours = []
            print(f"Found {len(files)} files for {run_date}")

            for filename in files[:5]:  # Check first 5 files
                filepath = os.path.join("Archive", filename)
                try:
                    mod_time = os.path.getmtime(filepath)
                    hour = datetime.datetime.fromtimestamp(mod_time).hour
                    upload_hours.append(hour)
                    print(f"  {filename}: uploaded at {hour}h")
                except Exception as e:
                    print(f"  {filename}: error getting time - {e}")

            if upload_hours:
                expected_min = min(upload_hours)
                expected_max = max(upload_hours)
                print(f"Expected range: {expected_min}h to {expected_max}h")

    # Process a small subset for testing
    try:
        result = process_grib_files_for_run(run_date)

        if not result.empty:
            if 'min_date_upload' in result.columns and 'max_date_upload' in result.columns:
                print(f"✅ Upload date columns found in batch processing!")
                print(f"📊 Data shape: {result.shape}")

                # Show upload date range
                min_date = result['min_date_upload'].iloc[0]
                max_date = result['max_date_upload'].iloc[0]
                print(f"📊 Upload date range: {min_date} to {max_date}")

                # Calculate time difference
                time_diff = max_date - min_date
                print(f"📊 Upload window: {time_diff}")

                # Verify all rows have the same values
                unique_mins = result['min_date_upload'].nunique()
                unique_maxs = result['max_date_upload'].nunique()
                print(f"📊 Consistency check: {unique_mins} unique min values, {unique_maxs} unique max values")

                if unique_mins == 1 and unique_maxs == 1:
                    print("✅ All rows have consistent upload date range")
                else:
                    print("❌ Inconsistent upload date values across rows")

            else:
                print("❌ Upload date columns not found in batch processing")
                print(f"Available columns: {list(result.columns)}")
        else:
            print("❌ No data processed in batch")

    except Exception as e:
        print(f"❌ Error in batch processing: {e}")

    print("\n🎉 Upload date range test completed!")
    print("\n💡 Usage:")
    print("   - min_date_upload: Earliest datetime when files were uploaded")
    print("   - max_date_upload: Latest datetime when files were uploaded")
    print("   - Values are full datetime objects (YYYY-MM-DD HH:MM:SS)")
    print("   - Same values across all rows for a given run date")

if __name__ == "__main__":
    test_upload_time_range()
