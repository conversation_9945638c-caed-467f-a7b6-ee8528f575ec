#!/usr/bin/env python3
"""
Quick test script for the enhanced GRIB processor function
"""

from grib_processor import process_grib_files_for_run, add_enhanced_columns
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_enhanced_function():
    """Test the enhanced function with a small dataset"""
    
    print("🧪 Testing Enhanced GRIB Processor Function")
    print("=" * 50)
    
    # Test parameters
    run_date = "2025-04-28-00"
    test_rows = 500  # Small subset for quick testing
    
    print(f"📅 Run date: {run_date}")
    print(f"📊 Testing with {test_rows} rows")
    
    try:
        # Step 1: Get basic data
        print("\n🔄 Step 1: Processing basic GRIB data...")
        basic_df = process_grib_files_for_run(run_date)
        
        if basic_df.empty or 'variable_check' in basic_df.columns:
            print("❌ No valid basic data available")
            return
        
        print(f"✅ Basic data loaded: {basic_df.shape[0]:,} rows")
        
        # Step 2: Test enhanced function
        print(f"\n🔄 Step 2: Testing enhanced function with {test_rows} rows...")
        test_subset = basic_df.head(test_rows)
        enhanced_df = add_enhanced_columns(test_subset)
        
        print(f"✅ Enhanced processing completed!")
        print(f"📊 Enhanced data: {enhanced_df.shape}")
        print(f"📋 Columns: {list(enhanced_df.columns)}")
        
        # Step 3: Verify results
        print("\n🔍 Step 3: Verifying results...")
        
        # Check for new columns
        expected_new_cols = ['precipitation', 'valid_time_fr', 'run_year', 'run_month', 
                           'run_day', 'run_hour', 'forecasted_year', 'forecasted_month', 
                           'forecasted_day', 'forecasted_hour']
        
        missing_cols = [col for col in expected_new_cols if col not in enhanced_df.columns]
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
        else:
            print("✅ All expected columns present")
        
        # Check precipitation values
        precip_stats = enhanced_df['precipitation'].describe()
        print(f"📊 Precipitation stats: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f}, mean={precip_stats['mean']:.1f} mm")
        
        # Check timezone conversion
        sample_utc = enhanced_df['valid_time'].iloc[0]
        sample_paris = enhanced_df['valid_time_fr'].iloc[0]
        print(f"🌍 Timezone test: {sample_utc} → {sample_paris}")
        
        # Check date components
        sample_row = enhanced_df.iloc[0]
        print(f"📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}")
        print(f"📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}")
        
        # Test decumulation for one grid point
        print("\n🔍 Step 4: Testing decumulation logic...")
        sample_lat = enhanced_df['latitude'].iloc[0]
        sample_lon = enhanced_df['longitude'].iloc[0]
        
        grid_data = enhanced_df[
            (enhanced_df['latitude'] == sample_lat) & 
            (enhanced_df['longitude'] == sample_lon)
        ].sort_values('valid_time')
        
        print(f"📍 Grid point: lat={sample_lat:.2f}, lon={sample_lon:.2f}")
        print(f"📊 Time steps: {len(grid_data)}")
        
        if len(grid_data) > 1:
            print("\n⏰ Decumulation verification:")
            print("Time\t\tTP\t\tPrecipitation")
            for _, row in grid_data.head(5).iterrows():
                time_str = row['valid_time_fr'].strftime('%H:%M')
                print(f"{time_str}\t\t{row['tp']:.6f}\t{row['precipitation']:.1f} mm")
        
        # Step 5: Export test
        print("\n📤 Step 5: Testing CSV export...")
        output_file = f"test_enhanced_data_{run_date.replace(':', '-')}.csv"
        enhanced_df.to_csv(output_file, index=False)
        
        import os
        file_size = os.path.getsize(output_file)
        print(f"✅ Test data exported to: {output_file}")
        print(f"📊 File size: {file_size:,} bytes")
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_function()
