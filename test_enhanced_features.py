#!/usr/bin/env python3
"""
Quick test script to verify the enhanced features:
1. Coordinate rounding to 1 decimal
2. Date filtering to next 2 days only
"""

print("🧪 Testing Enhanced Features")
print("=" * 40)

try:
    # Import the S3 GRIB processor
    from grib_processor_S3 import process_and_export_s3_grib_files
    import pandas as pd
    import os
    from datetime import datetime

    print("✅ Imports successful!")

    # Test with a small subset for speed
    test_run_date = "2025-04-28-00"
    print(f"\n🔄 Testing enhanced features for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        
        # Test 1: Coordinate Rounding
        print(f"\n🗺️ Test 1: Coordinate Rounding")
        print(f"   Latitude precision: {enhanced_df['latitude'].apply(lambda x: len(str(x).split('.')[-1]) if '.' in str(x) else 0).max()} decimal places")
        print(f"   Longitude precision: {enhanced_df['longitude'].apply(lambda x: len(str(x).split('.')[-1]) if '.' in str(x) else 0).max()} decimal places")
        print(f"   Sample coordinates: lat={enhanced_df['latitude'].iloc[0]}, lon={enhanced_df['longitude'].iloc[0]}")
        
        # Test 2: Date Filtering
        print(f"\n📅 Test 2: Date Filtering (Next 2 Days Only)")
        if 'forecasted_year' in enhanced_df.columns:
            unique_dates = enhanced_df[['forecasted_year', 'forecasted_month', 'forecasted_day']].drop_duplicates()
            unique_dates = unique_dates.sort_values(['forecasted_year', 'forecasted_month', 'forecasted_day'])
            
            print(f"   Number of unique forecast dates: {len(unique_dates)}")
            print(f"   Forecast dates included:")
            for _, row in unique_dates.iterrows():
                date_str = f"{int(row['forecasted_year'])}-{int(row['forecasted_month']):02d}-{int(row['forecasted_day']):02d}"
                count = len(enhanced_df[
                    (enhanced_df['forecasted_year'] == row['forecasted_year']) &
                    (enhanced_df['forecasted_month'] == row['forecasted_month']) &
                    (enhanced_df['forecasted_day'] == row['forecasted_day'])
                ])
                print(f"     {date_str}: {count:,} rows")
        
        # Test 3: Data Quality Check
        print(f"\n🔍 Test 3: Data Quality Check")
        print(f"   Total rows: {len(enhanced_df):,}")
        print(f"   Total files: {enhanced_df['file_name'].nunique()}")
        
        if 'precipitation' in enhanced_df.columns:
            precip_stats = enhanced_df['precipitation'].describe()
            print(f"   Precipitation range: {precip_stats['min']:.1f} to {precip_stats['max']:.1f} mm")
            print(f"   Non-zero precipitation values: {(enhanced_df['precipitation'] > 0).sum():,}")
        
        # Test 4: File Export
        print(f"\n📤 Test 4: File Export")
        print(f"   CSV file: {csv_file}")
        print(f"   File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        print(f"   Enhanced columns: {len(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample Enhanced Data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 
                      'forecasted_year', 'forecasted_month', 'forecasted_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head(3))
        
        print(f"\n🎉 All enhanced features working correctly!")
        
        # Verification Summary
        print(f"\n📊 Verification Summary:")
        coord_rounded = enhanced_df['latitude'].apply(lambda x: len(str(x).split('.')[-1]) if '.' in str(x) else 0).max() <= 1
        date_filtered = len(unique_dates) <= 2 if 'forecasted_year' in enhanced_df.columns else True
        
        print(f"   ✅ Coordinates rounded to 1 decimal: {coord_rounded}")
        print(f"   ✅ Data filtered to ≤2 days: {date_filtered}")
        print(f"   ✅ Enhanced columns present: {'precipitation' in enhanced_df.columns}")
        print(f"   ✅ CSV export successful: {csv_file is not None}")
        
        if coord_rounded and date_filtered:
            print(f"\n🎯 ALL TESTS PASSED! Enhanced features working perfectly!")
        else:
            print(f"\n⚠️ Some tests failed - check the output above")
            
    else:
        print("❌ Processing failed")

except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 40)
print("Enhanced features test completed!")
