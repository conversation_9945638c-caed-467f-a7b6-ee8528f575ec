# Import functions
from grib_processor import (
    parse_ecmwf_filename,
    add_enhanced_columns,
    validate_grib_file
)
import pandas as pd
import os
import boto3
import xarray as xr
import tempfile
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# S3 Configuration
aws_access_key_id = "********************"
aws_secret_access_key = "MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf"
region_name = 'eu-west-3'
bucket_name = 'ecmwf-forecast-data-source'

# Initialize S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=aws_access_key_id,
    aws_secret_access_key=aws_secret_access_key,
    region_name=region_name
)

print("Imports and S3 client initialization successful!")
print(f"S3 bucket: {bucket_name}")
print(f"S3 region: {region_name}")

# S3 Utility Functions
def list_s3_objects(prefix=""):
    """
    List all objects in the S3 bucket with optional prefix filter.

    Args:
        prefix (str): Prefix to filter objects

    Returns:
        list: List of S3 object keys
    """
    try:
        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
        if 'Contents' in response:
            return [obj['Key'] for obj in response['Contents']]
        return []
    except Exception as e:
        logger.error(f"Error listing S3 objects: {e}")
        return []

def get_s3_files_for_run_date(run_date):
    """
    Find all GRIB files in S3 bucket for a specific run date according to ECMWF conventions.

    Args:
        run_date (str): Date in format "YYYY-MM-DD-HH"

    Returns:
        list: List of S3 object keys matching the run date
    """
    try:
        # Parse the run date
        if len(run_date) == 10:  # YYYY-MM-DD format
            run_date += "-00"  # Default to 00 hour

        date_parts = run_date.split('-')
        if len(date_parts) != 4:
            raise ValueError(f"Invalid date format: {run_date}")

        run_month = date_parts[1]
        run_day = date_parts[2]
        run_hour = date_parts[3]

        logger.info(f"Looking for S3 files with run date: {run_month}/{run_day} {run_hour}:XX")

        # Get all objects from S3
        all_objects = list_s3_objects()
        matching_files = []

        for obj_key in all_objects:
            # Extract filename from object key (in case there are folders)
            filename = os.path.basename(obj_key)

            # Parse the filename according to ECMWF convention
            parsed = parse_ecmwf_filename(filename)
            if parsed is None:
                continue

            # Check if this file matches our run date
            if (parsed['run_month'] == run_month and
                parsed['run_day'] == run_day and
                parsed['run_hour'] == run_hour):
                matching_files.append(obj_key)

        logger.info(f"Found {len(matching_files)} S3 objects for run date {run_date}")
        return sorted(matching_files)

    except ValueError:
        logger.error(f"Invalid date format. Expected YYYY-MM-DD or YYYY-MM-DD-HH, got: {run_date}")
        raise ValueError(f"Invalid date format. Expected YYYY-MM-DD or YYYY-MM-DD-HH, got: {run_date}")

def download_s3_object_to_temp(object_key):
    """
    Download S3 object to a temporary file and return the path.

    Args:
        object_key (str): S3 object key

    Returns:
        str: Path to temporary file, or None if error
    """
    try:
        # Create temporary file with .grib extension
        tmp_file = tempfile.NamedTemporaryFile(suffix=".grib", delete=False)
        tmp_path = tmp_file.name
        tmp_file.close()  # Close immediately so S3 can write to it

        # Download from S3
        s3_client.download_file(bucket_name, object_key, tmp_path)
        logger.info(f"Downloaded S3 object {object_key} to {tmp_path}")
        return tmp_path

    except Exception as e:
        logger.error(f"Error downloading S3 object {object_key}: {e}")
        return None

def get_s3_object_metadata(object_key):
    """
    Get metadata for an S3 object including last modified date.

    Args:
        object_key (str): S3 object key

    Returns:
        dict: Object metadata or None if error
    """
    try:
        response = s3_client.head_object(Bucket=bucket_name, Key=object_key)
        return {
            'last_modified': response['LastModified'],
            'size': response['ContentLength'],
            'etag': response['ETag']
        }
    except Exception as e:
        logger.error(f"Error getting metadata for S3 object {object_key}: {e}")
        return None

def test_s3_file_access(object_key):
    """
    Test if we can access and read a specific S3 object.

    Args:
        object_key (str): S3 object key to test

    Returns:
        bool: True if accessible, False otherwise
    """
    try:
        # Try to get object metadata
        metadata = get_s3_object_metadata(object_key)
        if metadata:
            logger.info(f"✅ S3 object {object_key} is accessible (size: {metadata['size']} bytes)")
            return True
        return False
    except Exception as e:
        logger.error(f"❌ Cannot access S3 object {object_key}: {e}")
        return False

def validate_s3_grib_file(object_key):
    """
    Validate a GRIB file from S3 by downloading it temporarily and checking its contents.

    Args:
        object_key (str): S3 object key

    Returns:
        dict: Validation results with dataset if valid
    """
    temp_path = None
    try:
        # Download to temporary file
        temp_path = download_s3_object_to_temp(object_key)
        if temp_path is None:
            return {
                'file_name': os.path.basename(object_key),
                'variable_check': 0,
                'row_check': 0,
                'dataset': None
            }

        # Use the existing validation function
        result = validate_grib_file(temp_path)
        # Update the file name to reflect S3 object
        result['file_name'] = os.path.basename(object_key)
        return result

    except Exception as e:
        logger.error(f"Error validating S3 GRIB file {object_key}: {e}")
        return {
            'file_name': os.path.basename(object_key),
            'variable_check': 0,
            'row_check': 0,
            'dataset': None
        }
    finally:
        # Clean up temporary file
        if temp_path and os.path.exists(temp_path):
            os.remove(temp_path)

def process_s3_grib_files_for_run(run_date, check_availability=False, max_forecast_hours=72):
    """
    Process all GRIB files from S3 for a given run date.

    Args:
        run_date (str): Run date in format "YYYY-MM-DD" or "YYYY-MM-DD-HH"
        check_availability (bool): If True, check forecast file availability
        max_forecast_hours (int): Maximum forecast hours to check

    Returns:
        pandas.DataFrame: Compiled data from all valid files
    """
    logger.info(f"Processing S3 GRIB files for run date: {run_date}")

    # Step 1: Get all S3 objects for this run date
    try:
        s3_objects = get_s3_files_for_run_date(run_date)
    except ValueError as e:
        logger.error(f"Error getting S3 files: {e}")
        return pd.DataFrame()

    if not s3_objects:
        logger.warning(f"No S3 objects found for run date: {run_date}")
        return pd.DataFrame()

    # Step 2: Validate each S3 object
    validation_results = []
    valid_datasets = []

    for object_key in s3_objects:
        result = validate_s3_grib_file(object_key)
        validation_results.append({
            'file_name': result['file_name'],
            'variable_check': result['variable_check'],
            'row_check': result['row_check']
        })

        # If the file is valid, keep the dataset
        if result['dataset'] is not None:
            valid_datasets.append((result['file_name'], result['dataset']))

    # Step 3: Check if we have any valid data
    if not valid_datasets:
        logger.error("No valid datasets found")
        # Return validation results as DataFrame
        return pd.DataFrame(validation_results)

    logger.info(f"Found {len(valid_datasets)} valid datasets out of {len(s3_objects)} S3 objects")

    # Step 4: Compile data from valid datasets
    compiled_data = []

    for file_name, ds in valid_datasets:
        try:
            # Convert to DataFrame
            df = ds.to_dataframe()

            # Reset index to get latitude and longitude as columns
            df = df.reset_index()

            # Keep only required columns
            required_columns = ['latitude', 'longitude', 'valid_time', 'tp']

            # Check that all required columns are present
            available_columns = df.columns.tolist()
            missing_columns = [col for col in required_columns if col not in available_columns]

            if missing_columns:
                logger.warning(f"Missing columns in {file_name}: {missing_columns}")
                continue

            # Select required columns
            df_filtered = df[required_columns].copy()

            # Add file_name column
            df_filtered['file_name'] = file_name

            # Add time column (extracted from dataset)
            if 'time' in ds.coords:
                df_filtered['time'] = ds.coords['time'].values
            elif 'time' in ds.variables:
                df_filtered['time'] = ds.variables['time'].values
            else:
                logger.warning(f"No time coordinate found in {file_name}")
                df_filtered['time'] = None

            compiled_data.append(df_filtered)
            logger.info(f"✅ Processed {file_name}: {len(df_filtered)} rows")

        except Exception as e:
            logger.error(f"Error processing {file_name}: {e}")
            continue

    if not compiled_data:
        logger.error("No data could be compiled from valid files")
        return pd.DataFrame()

    # Step 5: Concatenate all DataFrames
    final_df = pd.concat(compiled_data, ignore_index=True)

    # Step 6: Add upload date range columns using S3 metadata
    logger.info("Adding upload date range columns from S3 metadata...")

    upload_dates = []

    # Collect last modified dates from all S3 objects
    for object_key in s3_objects:
        try:
            metadata = get_s3_object_metadata(object_key)
            if metadata and 'last_modified' in metadata:
                # Convert to datetime and remove timezone for consistency
                upload_date = metadata['last_modified'].replace(tzinfo=None)
                upload_dates.append(upload_date)
        except Exception as e:
            logger.warning(f"Could not get metadata for S3 object {object_key}: {e}")

    if upload_dates:
        min_date_upload = min(upload_dates)
        max_date_upload = max(upload_dates)

        # Format dates to minute precision (no seconds)
        min_date_formatted = min_date_upload.replace(second=0, microsecond=0)
        max_date_formatted = max_date_upload.replace(second=0, microsecond=0)

        # Add these columns to all rows
        final_df['min_date_upload'] = min_date_formatted
        final_df['max_date_upload'] = max_date_formatted

        logger.info(f"✅ Upload date range: {min_date_formatted} to {max_date_formatted}")
    else:
        final_df['min_date_upload'] = None
        final_df['max_date_upload'] = None
        logger.warning("Could not determine upload date range from S3 metadata")

    # Step 7: Reorder columns in specified order
    column_order = ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']
    final_df = final_df[column_order]

    logger.info(f"✅ Successfully compiled S3 data: {len(final_df)} total rows from {len(valid_datasets)} files")

    return final_df

def process_and_export_s3_grib_files(run_date, output_file=None, check_availability=False, max_forecast_hours=72, include_enhanced_columns=True):
    """
    Process GRIB files from S3 for a given date and export automatically to CSV.

    Args:
        run_date (str): Run date in format "YYYY-MM-DD-HH"
        output_file (str): Output CSV file name (optional)
        check_availability (bool): If True, check forecast file availability
        max_forecast_hours (int): Maximum forecast hours to check
        include_enhanced_columns (bool): If True, add enhanced columns (precipitation, timezone, dates) - DEFAULT: True

    Returns:
        pandas.DataFrame: Processed data
        str: Path to created CSV file
    """
    logger.info(f"Processing and exporting S3 GRIB files for run date: {run_date}")

    # Process the files from S3
    result = process_s3_grib_files_for_run(run_date, check_availability, max_forecast_hours)

    if result.empty:
        logger.warning("No data to export")
        return result, None

    # Add enhanced columns if requested and if this is compiled data
    if include_enhanced_columns and 'variable_check' not in result.columns and 'is_present' not in result.columns:
        logger.info("Adding enhanced columns (precipitation, timezone, date components)...")
        try:
            result = add_enhanced_columns(result)
            logger.info(f"✅ Enhanced columns added successfully")
        except Exception as e:
            logger.error(f"❌ Error adding enhanced columns: {e}")
            logger.info("Proceeding with basic data export...")

    # Determine output file name
    if output_file is None:
        # Create filename with enhanced format including max_date_upload
        if 'max_date_upload' in result.columns and result['max_date_upload'].iloc[0] is not None:
            max_date_str = result['max_date_upload'].iloc[0].strftime('%Y-%m-%d-%H-%M')
            output_file = f"grib_compiled_data_{run_date.replace(':', '-')}_upload_{max_date_str}.csv"
        else:
            # Fallback to basic format
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"grib_compiled_data_{run_date.replace(':', '-')}_{timestamp}.csv"

    # Export to CSV
    try:
        result.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"✅ Data exported to: {output_file}")
        logger.info(f"📊 Exported {len(result)} rows")
        if include_enhanced_columns and 'precipitation' in result.columns:
            logger.info(f"📊 Enhanced data includes: precipitation, timezone conversion, and date components")
        return result, output_file
    except Exception as e:
        logger.error(f"❌ Error exporting to CSV: {e}")
        return result, None

def process_single_s3_grib_file(object_key, output_file=None):
    """
    Process a single GRIB file from S3 and export to CSV for manual verification.

    Args:
        object_key (str): S3 object key
        output_file (str): Output CSV file name (optional)

    Returns:
        pandas.DataFrame: File data
        str: Path to created CSV file
        dict: Validation information
    """
    logger.info(f"Processing single S3 GRIB file: {object_key}")

    # Validate the S3 file
    validation_result = validate_s3_grib_file(object_key)

    file_name = os.path.basename(object_key)

    # Validation information
    validation_info = {
        'file_name': validation_result['file_name'],
        'variable_check_passed': validation_result['variable_check'] == 1,
        'row_check_passed': validation_result['row_check'] == 1,
        'dataset_loaded': validation_result['dataset'] is not None
    }

    if validation_result['dataset'] is None:
        logger.error(f"❌ Could not load S3 file: {file_name}")
        return pd.DataFrame(), None, validation_info

    try:
        # Convert to DataFrame
        ds = validation_result['dataset']
        df = ds.to_dataframe()
        df = df.reset_index()

        # Add additional information for manual analysis
        df['file_name'] = file_name

        # Add upload date columns for single file using S3 metadata
        metadata = get_s3_object_metadata(object_key)
        if metadata and 'last_modified' in metadata:
            modification_date = metadata['last_modified'].replace(tzinfo=None)
            # Format date to minute precision (no seconds)
            modification_date_formatted = modification_date.replace(second=0, microsecond=0)

            # For single file, min and max are identical
            df['min_date_upload'] = modification_date_formatted
            df['max_date_upload'] = modification_date_formatted
        else:
            df['min_date_upload'] = None
            df['max_date_upload'] = None

        # Add time column if available
        if 'time' in ds.coords:
            df['time'] = ds.coords['time'].values
        elif 'time' in ds.variables:
            df['time'] = ds.variables['time'].values

        # Reorder columns for better readability
        important_cols = ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']
        other_cols = [col for col in df.columns if col not in important_cols]
        df = df[important_cols + other_cols]

        # Determine output file name
        if output_file is None:
            base_name = os.path.splitext(file_name)[0]
            output_file = f"single_s3_file_check_{base_name}.csv"

        # Export to CSV
        df.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"✅ Single S3 file data exported to: {output_file}")
        logger.info(f"📊 File contains {len(df)} rows and {len(df.columns)} columns")

        # Display data summary
        logger.info(f"📋 Data summary for {file_name}:")
        logger.info(f"   - Latitude range: {df['latitude'].min():.2f} to {df['latitude'].max():.2f}")
        logger.info(f"   - Longitude range: {df['longitude'].min():.2f} to {df['longitude'].max():.2f}")
        logger.info(f"   - TP (precipitation) range: {df['tp'].min():.6f} to {df['tp'].max():.6f}")
        if 'valid_time' in df.columns:
            logger.info(f"   - Valid time: {df['valid_time'].iloc[0]}")

        return df, output_file, validation_info

    except Exception as e:
        logger.error(f"❌ Error processing single S3 file {file_name}: {e}")
        return pd.DataFrame(), None, validation_info

# 🧪 S3 CONNECTIVITY TEST
print("🚀 Testing S3 Connectivity and Enhanced GRIB Processor Function")
print("=" * 70)

# Test S3 connectivity first
print("\n🔄 Step 0: Testing S3 connectivity...")
try:
    # List first 10 objects to test connectivity
    all_objects = list_s3_objects()
    if all_objects:
        print(f"✅ S3 connectivity successful! Found {len(all_objects)} objects in bucket")
        print("📋 First 10 objects:")
        for i, obj in enumerate(all_objects[:10]):
            print(f"   {i+1}. {obj}")
        if len(all_objects) > 10:
            print(f"   ... and {len(all_objects) - 10} more objects")
    else:
        print("⚠️ S3 bucket appears to be empty or inaccessible")
except Exception as e:
    print(f"❌ S3 connectivity failed: {e}")
    print("Please check your AWS credentials and bucket configuration")

print("\n" + "=" * 70)

if __name__ == "__main__":
    # 🧪 ENHANCED FUNCTION TEST
    print("🚀 Testing Enhanced S3 GRIB Processor Function")
    print("=" * 50)

    # Test parameters
    run_date = "2025-04-28-00"
    test_rows = 100000000000  # Small subset for quick testing

    print(f"📅 Run date: {run_date}")
    print(f"📊 Testing with {test_rows} rows")

    try:
        # Step 1: Get basic data from S3
        print("\n🔄 Step 1: Processing basic GRIB data from S3...")
        basic_df = process_s3_grib_files_for_run(run_date)
        print("test longueur")
        print(len(basic_df))
        print(basic_df)
        
        if basic_df.empty or 'variable_check' in basic_df.columns:
            print("❌ No valid basic data available")
        else:
            print(f"✅ Basic data loaded: {basic_df.shape[0]:,} rows")
            print(f"📋 Basic columns: {list(basic_df.columns)}")
            
            # Step 2: Test enhanced S3 processing and export function
            print(f"\n🔄 Step 2: Testing enhanced S3 processing and export function...")
            enhanced_df, csv_file = process_and_export_s3_grib_files(run_date, include_enhanced_columns=True)

            if csv_file:
                print(f"✅ Enhanced S3 processing completed!")
                print(f"📊 Enhanced data: {enhanced_df.shape}")
                print(f"📋 Enhanced columns: {list(enhanced_df.columns)}")
                print(f"📤 Data exported to: {csv_file}")
                print(f"📊 File size: {os.path.getsize(csv_file):,} bytes")

                # Step 3: Show sample data
                print("\n📋 Sample enhanced data:")
                if 'valid_time_fr' in enhanced_df.columns:
                    display_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation',
                                   'run_month', 'run_day', 'forecasted_hour']
                    available_cols = [col for col in display_cols if col in enhanced_df.columns]
                    print(enhanced_df[available_cols].head())
                else:
                    print(enhanced_df.head())

                # Step 4: Quick verification
                print("\n🔍 Quick Verification:")

                # Check precipitation stats if available
                if 'precipitation' in enhanced_df.columns:
                    precip_stats = enhanced_df['precipitation'].describe()
                    print(f"   📊 Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm")

                # Check timezone conversion if available
                if 'valid_time_fr' in enhanced_df.columns:
                    sample_utc = enhanced_df['valid_time'].iloc[0]
                    sample_paris = enhanced_df['valid_time_fr'].iloc[0]
                    print(f"   🌍 Timezone: {sample_utc} → {sample_paris}")

                # Check date components if available
                if 'run_year' in enhanced_df.columns:
                    sample_row = enhanced_df.iloc[0]
                    print(f"   📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}")
                    print(f"   📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}")

                # Check S3 metadata
                if 'min_date_upload' in enhanced_df.columns:
                    print(f"   📅 Upload date range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}")

                print("\n🎉 Enhanced S3 function test completed successfully!")
            else:
                print("❌ Enhanced S3 processing failed - no CSV file created")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
    traceback.print_exc()
