# Import functions
from grib_processor import (
    parse_ecmwf_filename,
    get_files_for_run_date,
    process_and_export_grib_files,
    process_single_grib_file,
    process_grib_files_for_run,
    add_enhanced_columns
)
import pandas as pd
import os

print("Imports successful!")
print(f"Archive folder exists: {os.path.exists('Archive')}")

# 🧪 ENHANCED FUNCTION TEST
print("🚀 Testing Enhanced GRIB Processor Function")
print("=" * 50)

# Test parameters
run_date = "2025-04-28-00"
test_rows = 100000000000  # Small subset for quick testing

print(f"📅 Run date: {run_date}")
print(f"📊 Testing with {test_rows} rows")

try:
    # Step 1: Get basic data
    print("\n🔄 Step 1: Processing basic GRIB data...")
    basic_df = process_grib_files_for_run(run_date)
    print("test longueur")
    print(len(basic_df))
    print(basic_df)
    
    if basic_df.empty or 'variable_check' in basic_df.columns:
        print("❌ No valid basic data available")
    else:
        print(f"✅ Basic data loaded: {basic_df.shape[0]:,} rows")
        print(f"📋 Basic columns: {list(basic_df.columns)}")
        
        # Step 2: Test enhanced function
        print(f"\n🔄 Step 2: Testing enhanced function...")
        # test_subset = basic_df.head(test_rows)
        test_subset = basic_df
        enhanced_df = add_enhanced_columns(test_subset)
        
        print(f"✅ Enhanced processing completed!")
        print(f"📊 Enhanced data: {enhanced_df.shape}")
        print(f"📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Step 3: Show sample data
        print("\n📋 Sample enhanced data:")
        display_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation', 
                       'run_month', 'run_day', 'forecasted_hour']
        print(enhanced_df[display_cols].head())
        
        # Step 4: Quick verification
        print("\n🔍 Quick Verification:")
        
        # Check precipitation stats
        precip_stats = enhanced_df['precipitation'].describe()
        print(f"   📊 Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm")
        
        # Check timezone conversion
        sample_utc = enhanced_df['valid_time'].iloc[0]
        sample_paris = enhanced_df['valid_time_fr'].iloc[0]
        print(f"   🌍 Timezone: {sample_utc} → {sample_paris}")
        
        # Check date components
        sample_row = enhanced_df.iloc[0]
        print(f"   📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}")
        print(f"   📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}")
        
        # round latitude and longitude
        enhanced_df['latitude'] = enhanced_df['latitude'].round(1)
        enhanced_df['longitude'] = enhanced_df['longitude'].round(1)


        # Step 5: Export test
        print("\n📤 Step 5: Testing CSV export...")
        output_file = f"enhanced_test_data_{run_date.replace(':', '-')}.csv"
        enhanced_df.to_csv(output_file, index=False)
        
        print(f"✅ Test data exported to: {output_file}")
        print(f"📊 File size: {os.path.getsize(output_file):,} bytes")
        
        print("\n🎉 Enhanced function test completed successfully!")
        
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()