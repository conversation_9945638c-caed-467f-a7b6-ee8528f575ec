# Import functions
from grib_processor import (
    parse_ecmwf_filename,
    add_enhanced_columns,
    validate_grib_file,
    export_to_french_csv,
    upload_to_s3
)
import pandas as pd
import os
import boto3
import xarray as xr
import tempfile
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# S3 Configuration
aws_access_key_id = "********************"
aws_secret_access_key = "MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf"
region_name = 'eu-west-3'
bucket_name = 'ecmwf-forecast-data-source'

# Initialize S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=aws_access_key_id,
    aws_secret_access_key=aws_secret_access_key,
    region_name=region_name
)

print("Imports and S3 client initialization successful!")
print(f"S3 bucket: {bucket_name}")
print(f"S3 region: {region_name}")

# S3 Utility Functions
def list_s3_objects(prefix=""):
    """
    List all objects in the S3 bucket with optional prefix filter.

    Args:
        prefix (str): Prefix to filter objects

    Returns:
        list: List of S3 object keys
    """
    try:
        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
        if 'Contents' in response:
            return [obj['Key'] for obj in response['Contents']]
        return []
    except Exception as e:
        logger.error(f"Error listing S3 objects: {e}")
        return []

def get_s3_files_for_run_date(run_date):
    """
    Find all GRIB files in S3 bucket for a specific run date according to ECMWF conventions.
    Uses efficient prefix filtering based on run hour:
    - Runs 00 and 12: Search for A1D* files
    - Runs 06 and 18: Search for A1S* files

    Args:
        run_date (str): Date in format "YYYY-MM-DD-HH"

    Returns:
        list: List of S3 object keys matching the run date
    """
    try:
        # Parse the run date
        if len(run_date) == 10:  # YYYY-MM-DD format
            run_date += "-00"  # Default to 00 hour

        date_parts = run_date.split('-')
        if len(date_parts) != 4:
            raise ValueError(f"Invalid date format: {run_date}")

        run_month = date_parts[1]
        run_day = date_parts[2]
        run_hour = date_parts[3]

        # Determine the correct prefix based on run hour
        if run_hour in ['00', '12']:
            prefix = 'A1D'
        elif run_hour in ['06', '18']:
            prefix = 'A1S'
        else:
            logger.warning(f"Unusual run hour: {run_hour}. Expected 00, 06, 12, or 18. Using A1D prefix as fallback.")
            prefix = 'A1D'

        logger.info(f"Looking for S3 files with run date: {run_month}/{run_day} {run_hour}:XX using prefix: {prefix}")

        # Get objects from S3 with the appropriate prefix for efficiency
        all_objects = list_s3_objects(prefix=prefix)
        matching_files = []

        for obj_key in all_objects:
            # Extract filename from object key (in case there are folders)
            filename = os.path.basename(obj_key)

            # Parse the filename according to ECMWF convention
            parsed = parse_ecmwf_filename(filename)
            if parsed is None:
                continue

            # Check if this file matches our run date
            if (parsed['run_month'] == run_month and
                parsed['run_day'] == run_day and
                parsed['run_hour'] == run_hour):
                matching_files.append(obj_key)

        logger.info(f"Found {len(matching_files)} S3 objects for run date {run_date} with prefix {prefix}")
        return sorted(matching_files)

    except ValueError:
        logger.error(f"Invalid date format. Expected YYYY-MM-DD or YYYY-MM-DD-HH, got: {run_date}")
        raise ValueError(f"Invalid date format. Expected YYYY-MM-DD or YYYY-MM-DD-HH, got: {run_date}")

def download_s3_object_to_temp(object_key):
    """
    Download S3 object to a temporary file and return the path.

    Args:
        object_key (str): S3 object key

    Returns:
        str: Path to temporary file, or None if error
    """
    try:
        # Create temporary file with .grib extension
        tmp_file = tempfile.NamedTemporaryFile(suffix=".grib", delete=False)
        tmp_path = tmp_file.name
        tmp_file.close()  # Close immediately so S3 can write to it

        # Download from S3
        s3_client.download_file(bucket_name, object_key, tmp_path)
        logger.info(f"Downloaded S3 object {object_key} to {tmp_path}")
        return tmp_path

    except Exception as e:
        logger.error(f"Error downloading S3 object {object_key}: {e}")
        return None

def get_s3_object_metadata(object_key):
    """
    Get metadata for an S3 object including last modified date.

    Args:
        object_key (str): S3 object key

    Returns:
        dict: Object metadata or None if error
    """
    try:
        response = s3_client.head_object(Bucket=bucket_name, Key=object_key)
        return {
            'last_modified': response['LastModified'],
            'size': response['ContentLength'],
            'etag': response['ETag']
        }
    except Exception as e:
        logger.error(f"Error getting metadata for S3 object {object_key}: {e}")
        return None

def test_s3_file_access(object_key):
    """
    Test if we can access and read a specific S3 object.

    Args:
        object_key (str): S3 object key to test

    Returns:
        bool: True if accessible, False otherwise
    """
    try:
        # Try to get object metadata
        metadata = get_s3_object_metadata(object_key)
        if metadata:
            logger.info(f"✅ S3 object {object_key} is accessible (size: {metadata['size']} bytes)")
            return True
        return False
    except Exception as e:
        logger.error(f"❌ Cannot access S3 object {object_key}: {e}")
        return False

def validate_s3_grib_file(object_key):
    """
    Validate a GRIB file from S3 by downloading it temporarily and checking its contents.

    Args:
        object_key (str): S3 object key

    Returns:
        dict: Validation results with dataset if valid
    """
    temp_path = None
    try:
        # Download to temporary file
        temp_path = download_s3_object_to_temp(object_key)
        if temp_path is None:
            return {
                'file_name': os.path.basename(object_key),
                'variable_check': 0,
                'row_check': 0,
                'dataset': None
            }

        # Use the existing validation function
        result = validate_grib_file(temp_path)
        # Update the file name to reflect S3 object
        result['file_name'] = os.path.basename(object_key)
        return result

    except Exception as e:
        logger.error(f"Error validating S3 GRIB file {object_key}: {e}")
        return {
            'file_name': os.path.basename(object_key),
            'variable_check': 0,
            'row_check': 0,
            'dataset': None
        }
    finally:
        # Clean up temporary file
        if temp_path and os.path.exists(temp_path):
            os.remove(temp_path)

def process_s3_grib_files_for_run(run_date, check_availability=False, max_forecast_hours=72):
    """
    Process all GRIB files from S3 for a given run date.

    Args:
        run_date (str): Run date in format "YYYY-MM-DD" or "YYYY-MM-DD-HH"
        check_availability (bool): If True, check forecast file availability
        max_forecast_hours (int): Maximum forecast hours to check

    Returns:
        pandas.DataFrame: Compiled data from all valid files
    """
    logger.info(f"Processing S3 GRIB files for run date: {run_date}")

    # Step 1: Get all S3 objects for this run date
    try:
        s3_objects = get_s3_files_for_run_date(run_date)
    except ValueError as e:
        logger.error(f"Error getting S3 files: {e}")
        return pd.DataFrame()

    if not s3_objects:
        logger.warning(f"No S3 objects found for run date: {run_date}")
        return pd.DataFrame()

    # Step 2: Validate each S3 object
    validation_results = []
    valid_datasets = []

    for object_key in s3_objects:
        result = validate_s3_grib_file(object_key)
        validation_results.append({
            'file_name': result['file_name'],
            'variable_check': result['variable_check'],
            'row_check': result['row_check']
        })

        # If the file is valid, keep the dataset
        if result['dataset'] is not None:
            valid_datasets.append((result['file_name'], result['dataset']))

    # Step 3: Check if we have any valid data
    if not valid_datasets:
        logger.error("No valid datasets found")
        # Return validation results as DataFrame
        return pd.DataFrame(validation_results)

    logger.info(f"Found {len(valid_datasets)} valid datasets out of {len(s3_objects)} S3 objects")

    # Step 4: Compile data from valid datasets
    compiled_data = []

    for file_name, ds in valid_datasets:
        try:
            # Convert to DataFrame
            df = ds.to_dataframe()

            # Reset index to get latitude and longitude as columns
            df = df.reset_index()

            # Keep only required columns
            required_columns = ['latitude', 'longitude', 'valid_time', 'tp']

            # Check that all required columns are present
            available_columns = df.columns.tolist()
            missing_columns = [col for col in required_columns if col not in available_columns]

            if missing_columns:
                logger.warning(f"Missing columns in {file_name}: {missing_columns}")
                continue

            # Select required columns
            df_filtered = df[required_columns].copy()

            # Add file_name column
            df_filtered['file_name'] = file_name

            # Add time column (extracted from dataset)
            if 'time' in ds.coords:
                df_filtered['time'] = ds.coords['time'].values
            elif 'time' in ds.variables:
                df_filtered['time'] = ds.variables['time'].values
            else:
                logger.warning(f"No time coordinate found in {file_name}")
                df_filtered['time'] = None

            compiled_data.append(df_filtered)
            logger.info(f"✅ Processed {file_name}: {len(df_filtered)} rows")

        except Exception as e:
            logger.error(f"Error processing {file_name}: {e}")
            continue

    if not compiled_data:
        logger.error("No data could be compiled from valid files")
        return pd.DataFrame()

    # Step 5: Concatenate all DataFrames
    final_df = pd.concat(compiled_data, ignore_index=True)

    # Step 6: Add upload date range columns using S3 metadata
    logger.info("Adding upload date range columns from S3 metadata...")

    upload_dates = []

    # Collect last modified dates from all S3 objects
    for object_key in s3_objects:
        try:
            metadata = get_s3_object_metadata(object_key)
            if metadata and 'last_modified' in metadata:
                # Convert to datetime and remove timezone for consistency
                upload_date = metadata['last_modified'].replace(tzinfo=None)
                upload_dates.append(upload_date)
        except Exception as e:
            logger.warning(f"Could not get metadata for S3 object {object_key}: {e}")

    if upload_dates:
        min_date_upload = min(upload_dates)
        max_date_upload = max(upload_dates)

        # Format dates to minute precision (no seconds)
        min_date_formatted = min_date_upload.replace(second=0, microsecond=0)
        max_date_formatted = max_date_upload.replace(second=0, microsecond=0)

        # Add these columns to all rows
        final_df['min_date_upload'] = min_date_formatted
        final_df['max_date_upload'] = max_date_formatted

        logger.info(f"✅ Upload date range: {min_date_formatted} to {max_date_formatted}")
    else:
        final_df['min_date_upload'] = None
        final_df['max_date_upload'] = None
        logger.warning("Could not determine upload date range from S3 metadata")

    # Step 7: Reorder columns in specified order
    column_order = ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']
    final_df = final_df[column_order]

    logger.info(f"✅ Successfully compiled S3 data: {len(final_df)} total rows from {len(valid_datasets)} files")

    return final_df

def process_and_export_s3_grib_files(run_date, output_file=None, check_availability=False, max_forecast_hours=72, include_enhanced_columns=True, upload_to_s3_bucket=True, output_format='csv', upload_locally=True, output_format_locally='csv'):
    """
    Process GRIB files from S3 for a given date and export automatically to CSV or Parquet.

    Args:
        run_date (str): Run date in format "YYYY-MM-DD-HH"
        output_file (str): Output file name (optional, extension will be added based on format)
        check_availability (bool): If True, check forecast file availability
        max_forecast_hours (int): Maximum forecast hours to check
        include_enhanced_columns (bool): If True, add enhanced columns (precipitation, timezone, dates) - DEFAULT: True
        upload_to_s3_bucket (bool): If True, upload file to S3 - DEFAULT: True
        output_format (str): S3 output format - 'csv' for French CSV or 'parquet' for Parquet - DEFAULT: 'csv'
        upload_locally (bool): If True, save file locally - DEFAULT: True
        output_format_locally (str): Local output format - 'csv' for French CSV or 'parquet' for Parquet - DEFAULT: 'csv'

    Returns:
        pandas.DataFrame: Processed data
        str: Path to created local file (if upload_locally=True), or S3 filename (if upload_locally=False)
    """
    logger.info(f"Processing and exporting S3 GRIB files for run date: {run_date}")

    # Process the files from S3
    result = process_s3_grib_files_for_run(run_date, check_availability, max_forecast_hours)

    if result.empty:
        logger.warning("No data to export")
        return result, None

    # Add enhanced columns if requested and if this is compiled data
    if include_enhanced_columns and 'variable_check' not in result.columns and 'is_present' not in result.columns:
        logger.info("Adding enhanced columns (precipitation, timezone, date components)...")
        try:
            result = add_enhanced_columns(result)
            logger.info(f"✅ Enhanced columns added successfully")
        except Exception as e:
            logger.error(f"❌ Error adding enhanced columns: {e}")
            logger.info("Proceeding with basic data export...")

    # Determine output file names for local and S3
    if output_file is None:
        # Create base filename
        if 'max_date_upload' in result.columns and result['max_date_upload'].iloc[0] is not None:
            max_date_str = result['max_date_upload'].iloc[0].strftime('%Y-%m-%d-%H-%M')
            base_filename = f"ecmwf_{run_date.replace(':', '-')}_upload_{max_date_str}"
        else:
            # Fallback to basic format
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_filename = f"ecmwf_{run_date.replace(':', '-')}_{timestamp}"

        # Create local filename
        local_extension = 'parquet' if output_format_locally == 'parquet' else 'csv'
        local_output_file = f"{base_filename}.{local_extension}"

        # Create S3 filename
        s3_extension = 'parquet' if output_format == 'parquet' else 'csv'
        s3_output_file = f"{base_filename}.{s3_extension}"
    else:
        # User provided filename - use as base and add appropriate extensions
        base_name = os.path.splitext(output_file)[0]
        local_extension = 'parquet' if output_format_locally == 'parquet' else 'csv'
        s3_extension = 'parquet' if output_format == 'parquet' else 'csv'

        local_output_file = f"{base_name}.{local_extension}"
        s3_output_file = f"{base_name}.{s3_extension}"

    # Export files based on parameters
    try:
        local_file_path = None
        s3_file_path = None

        # Step 1: Create local file if requested
        if upload_locally:
            logger.info(f"📁 Creating local file in {output_format_locally.upper()} format...")

            if output_format_locally == 'parquet':
                # Export to Parquet format locally
                result_parquet = result.copy()

                # Convert date columns to string format for French compatibility
                date_columns = ['time', 'valid_time', 'valid_time_fr', 'min_date_upload', 'max_date_upload']
                for col in date_columns:
                    if col in result_parquet.columns:
                        if pd.api.types.is_datetime64_any_dtype(result_parquet[col]):
                            result_parquet[col] = result_parquet[col].dt.strftime('%d/%m/%Y %H:%M')

                # Save as Parquet locally
                result_parquet.to_parquet(local_output_file, index=False, engine='pyarrow')
                local_file_path = local_output_file
                logger.info(f"✅ Local Parquet file created: {local_file_path}")

            else:
                # Export in French CSV format locally
                from grib_processor import export_to_french_csv
                local_file_path = export_to_french_csv(result, local_output_file)
                logger.info(f"✅ Local French CSV file created: {local_file_path}")

        # Step 2: Create S3 file if requested
        if upload_to_s3_bucket and 'variable_check' not in result.columns and 'is_present' not in result.columns:
            logger.info(f"☁️ Preparing S3 file in {output_format.upper()} format...")

            if output_format == 'parquet':
                # Create Parquet for S3
                result_s3_parquet = result.copy()

                # Convert date columns to string format for French compatibility
                date_columns = ['time', 'valid_time', 'valid_time_fr', 'min_date_upload', 'max_date_upload']
                for col in date_columns:
                    if col in result_s3_parquet.columns:
                        if pd.api.types.is_datetime64_any_dtype(result_s3_parquet[col]):
                            result_s3_parquet[col] = result_s3_parquet[col].dt.strftime('%d/%m/%Y %H:%M')

                # Save as Parquet for S3
                result_s3_parquet.to_parquet(s3_output_file, index=False, engine='pyarrow')
                s3_file_path = s3_output_file

            else:
                # Create French CSV for S3
                from grib_processor import export_to_french_csv
                s3_file_path = export_to_french_csv(result, s3_output_file)

            # Upload to S3
            from grib_processor import S3_UPLOAD_BUCKET, upload_to_s3
            logger.info(f"🔄 Uploading {output_format.upper()} file to S3 bucket: {S3_UPLOAD_BUCKET}")
            s3_success = upload_to_s3(s3_file_path, os.path.basename(s3_file_path), S3_UPLOAD_BUCKET)

            if s3_success:
                logger.info(f"✅ Successfully uploaded to S3: s3://{S3_UPLOAD_BUCKET}/{os.path.basename(s3_file_path)}")

                # Clean up S3 temp file if different from local file
                if s3_file_path != local_file_path and os.path.exists(s3_file_path):
                    os.remove(s3_file_path)
                    logger.info(f"🧹 Cleaned up temporary S3 file: {s3_file_path}")
            else:
                logger.warning(f"⚠️ S3 upload failed")

        # Summary
        logger.info(f"📊 Processed {len(result)} rows")
        if include_enhanced_columns and 'precipitation' in result.columns:
            logger.info(f"📊 Enhanced data includes: precipitation, timezone conversion, and date components")

        if upload_locally and local_file_path:
            logger.info(f"📁 Local file: {local_file_path} ({output_format_locally.upper()})")
        if upload_to_s3_bucket:
            logger.info(f"☁️ S3 file: {os.path.basename(s3_file_path) if s3_file_path else 'Upload failed'} ({output_format.upper()})")

        # Return the local file path if available, otherwise the S3 filename
        return_file_path = local_file_path if local_file_path else (os.path.basename(s3_file_path) if s3_file_path else None)
        return result, return_file_path

    except Exception as e:
        logger.error(f"❌ Error during export process: {e}")
        return result, None

def process_single_s3_grib_file(object_key, output_file=None):
    """
    Process a single GRIB file from S3 and export to CSV for manual verification.

    Args:
        object_key (str): S3 object key
        output_file (str): Output CSV file name (optional)

    Returns:
        pandas.DataFrame: File data
        str: Path to created CSV file
        dict: Validation information
    """
    logger.info(f"Processing single S3 GRIB file: {object_key}")

    # Validate the S3 file
    validation_result = validate_s3_grib_file(object_key)

    file_name = os.path.basename(object_key)

    # Validation information
    validation_info = {
        'file_name': validation_result['file_name'],
        'variable_check_passed': validation_result['variable_check'] == 1,
        'row_check_passed': validation_result['row_check'] == 1,
        'dataset_loaded': validation_result['dataset'] is not None
    }

    if validation_result['dataset'] is None:
        logger.error(f"❌ Could not load S3 file: {file_name}")
        return pd.DataFrame(), None, validation_info

    try:
        # Convert to DataFrame
        ds = validation_result['dataset']
        df = ds.to_dataframe()
        df = df.reset_index()

        # Add additional information for manual analysis
        df['file_name'] = file_name

        # Add upload date columns for single file using S3 metadata
        metadata = get_s3_object_metadata(object_key)
        if metadata and 'last_modified' in metadata:
            modification_date = metadata['last_modified'].replace(tzinfo=None)
            # Format date to minute precision (no seconds)
            modification_date_formatted = modification_date.replace(second=0, microsecond=0)

            # For single file, min and max are identical
            df['min_date_upload'] = modification_date_formatted
            df['max_date_upload'] = modification_date_formatted
        else:
            df['min_date_upload'] = None
            df['max_date_upload'] = None

        # Add time column if available
        if 'time' in ds.coords:
            df['time'] = ds.coords['time'].values
        elif 'time' in ds.variables:
            df['time'] = ds.variables['time'].values

        # Reorder columns for better readability
        important_cols = ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']
        other_cols = [col for col in df.columns if col not in important_cols]
        df = df[important_cols + other_cols]

        # Determine output file name
        if output_file is None:
            base_name = os.path.splitext(file_name)[0]
            output_file = f"single_s3_file_check_{base_name}.csv"

        # Export to CSV
        df.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"✅ Single S3 file data exported to: {output_file}")
        logger.info(f"📊 File contains {len(df)} rows and {len(df.columns)} columns")

        # Display data summary
        logger.info(f"📋 Data summary for {file_name}:")
        logger.info(f"   - Latitude range: {df['latitude'].min():.2f} to {df['latitude'].max():.2f}")
        logger.info(f"   - Longitude range: {df['longitude'].min():.2f} to {df['longitude'].max():.2f}")
        logger.info(f"   - TP (precipitation) range: {df['tp'].min():.6f} to {df['tp'].max():.6f}")
        if 'valid_time' in df.columns:
            logger.info(f"   - Valid time: {df['valid_time'].iloc[0]}")

        return df, output_file, validation_info

    except Exception as e:
        logger.error(f"❌ Error processing single S3 file {file_name}: {e}")
        return pd.DataFrame(), None, validation_info

def get_latest_available_run_date(current_datetime=None):
    """
    Get the latest run date available in S3 bucket ecmwf-forecast-by-run-date.

    Args:
        current_datetime (datetime or str): Current date and time. If None, uses datetime.now()
                                          Can be datetime object or string in format "YYYY-MM-DD HH:MM:SS"

    Returns:
        str: Latest available run date in format "YYYY-MM-DD-HH" or None if no files found
    """
    from datetime import datetime, timedelta
    import re

    # Handle input datetime
    if current_datetime is None:
        current_dt = datetime.now()
    elif isinstance(current_datetime, str):
        try:
            current_dt = datetime.strptime(current_datetime, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                current_dt = datetime.strptime(current_datetime, '%Y-%m-%d %H:%M')
            except ValueError:
                logger.error(f"Invalid datetime format: {current_datetime}. Use 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD HH:MM'")
                return None
    else:
        current_dt = current_datetime

    logger.info(f"🔍 Searching for latest run date available as of: {current_dt.strftime('%Y-%m-%d %H:%M:%S')}")

    # Create S3 client for the upload bucket
    upload_bucket = "ecmwf-forecast-by-run-date"
    try:
        upload_s3_client = boto3.client(
            's3',
            aws_access_key_id="********************",
            aws_secret_access_key="MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf",
            region_name='eu-west-3'
        )

        logger.info(f"📡 Connected to S3 bucket: {upload_bucket}")

        # List all objects in the bucket
        response = upload_s3_client.list_objects_v2(Bucket=upload_bucket)

        if 'Contents' not in response:
            logger.warning(f"⚠️ No files found in bucket '{upload_bucket}'")
            return None

        # Extract run dates from filenames
        run_dates = set()
        filename_pattern = re.compile(r'ecmwf_(\d{4}-\d{2}-\d{2}-\d{2})_')

        for obj in response['Contents']:
            filename = obj['Key']
            match = filename_pattern.match(filename)
            if match:
                run_date = match.group(1)
                run_dates.add(run_date)

        if not run_dates:
            logger.warning(f"⚠️ No valid ECMWF run date files found in bucket '{upload_bucket}'")
            return None

        # Sort run dates and find the latest one that's not in the future
        sorted_run_dates = sorted(run_dates, reverse=True)

        for run_date in sorted_run_dates:
            try:
                # Parse run date
                run_dt = datetime.strptime(run_date, '%Y-%m-%d-%H')

                # Check if this run date is not in the future compared to current time
                if run_dt <= current_dt:
                    logger.info(f"✅ Latest available run date found: {run_date}")
                    logger.info(f"📅 Run time: {run_dt.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"🕐 Current time: {current_dt.strftime('%Y-%m-%d %H:%M:%S')}")
                    return run_date
                else:
                    logger.debug(f"⏭️ Skipping future run date: {run_date}")

            except ValueError:
                logger.warning(f"⚠️ Invalid run date format: {run_date}")
                continue

        logger.warning(f"⚠️ No run dates found that are not in the future")
        return None

    except Exception as e:
        logger.error(f"❌ Error accessing S3 bucket '{upload_bucket}': {e}")
        return None


def get_latest_available_source_run_date(current_datetime=None):
    """
    Get the latest run date available in S3 bucket ecmwf-forecast-data-source (raw GRIB files).

    Args:
        current_datetime (datetime or str): Current date and time. If None, uses datetime.now()
                                          Can be datetime object or string in format "YYYY-MM-DD HH:MM:SS"

    Returns:
        str: Latest available run date in format "YYYY-MM-DD-HH" or None if no files found
    """
    from datetime import datetime, timedelta
    import re

    # Handle input datetime
    if current_datetime is None:
        current_dt = datetime.now()
    elif isinstance(current_datetime, str):
        try:
            current_dt = datetime.strptime(current_datetime, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                current_dt = datetime.strptime(current_datetime, '%Y-%m-%d %H:%M')
            except ValueError:
                logger.error(f"Invalid datetime format: {current_datetime}. Use 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD HH:MM'")
                return None
    else:
        current_dt = current_datetime

    logger.info(f"🔍 Searching for latest source run date available as of: {current_dt.strftime('%Y-%m-%d %H:%M:%S')}")

    # Use the source S3 bucket (raw GRIB files)
    source_bucket = bucket_name  # ecmwf-forecast-data-source
    try:
        logger.info(f"📡 Connected to source S3 bucket: {source_bucket}")

        # Get all objects in the bucket (we'll need to check multiple prefixes)
        all_run_dates = set()

        # Check both A1D and A1S prefixes for different run hours
        prefixes = ['A1D', 'A1S']

        for prefix in prefixes:
            try:
                response = s3_client.list_objects_v2(Bucket=source_bucket, Prefix=prefix)

                if 'Contents' in response:
                    logger.info(f"📋 Found {len(response['Contents'])} objects with prefix {prefix}")

                    # Extract run dates from ECMWF filenames
                    # Format: ccSMMDDHHIImmddhhiiE where MMDDHHII is run date/time
                    filename_pattern = re.compile(r'A1[DS](\d{2})(\d{2})(\d{2})(\d{2})')

                    for obj in response['Contents']:
                        filename = obj['Key']
                        match = filename_pattern.match(filename)
                        if match:
                            month, day, hour, _ = match.groups()

                            # Determine year (assume current year or previous year if month is in future)
                            current_year = current_dt.year
                            file_month = int(month)
                            file_day = int(day)
                            file_hour = int(hour)

                            # If the month is significantly in the future, assume it's from previous year
                            if file_month > current_dt.month + 1:
                                year = current_year - 1
                            else:
                                year = current_year

                            try:
                                # Create run date string
                                run_date = f"{year:04d}-{file_month:02d}-{file_day:02d}-{file_hour:02d}"
                                all_run_dates.add(run_date)
                            except ValueError:
                                # Invalid date, skip
                                continue
                else:
                    logger.debug(f"No objects found with prefix {prefix}")

            except Exception as e:
                logger.warning(f"⚠️ Error checking prefix {prefix}: {e}")
                continue

        if not all_run_dates:
            logger.warning(f"⚠️ No valid ECMWF run date files found in source bucket '{source_bucket}'")
            return None

        # Sort run dates and find the latest one that's not in the future
        sorted_run_dates = sorted(all_run_dates, reverse=True)

        logger.info(f"📊 Found {len(sorted_run_dates)} unique run dates in source bucket")
        logger.debug(f"🔍 Latest 5 run dates: {sorted_run_dates[:5]}")

        for run_date in sorted_run_dates:
            try:
                # Parse run date
                run_dt = datetime.strptime(run_date, '%Y-%m-%d-%H')

                # Check if this run date is not in the future compared to current time
                if run_dt <= current_dt:
                    logger.info(f"✅ Latest available source run date found: {run_date}")
                    logger.info(f"📅 Run time: {run_dt.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"🕐 Current time: {current_dt.strftime('%Y-%m-%d %H:%M:%S')}")
                    return run_date
                else:
                    logger.debug(f"⏭️ Skipping future run date: {run_date}")

            except ValueError:
                logger.warning(f"⚠️ Invalid run date format: {run_date}")
                continue

        logger.warning(f"⚠️ No source run dates found that are not in the future")
        return None

    except Exception as e:
        logger.error(f"❌ Error accessing source S3 bucket '{source_bucket}': {e}")
        return None


def create_meteomatics_format_df(filtered_df, run_date, latitude, longitude, activity_start_date, activity_start_hour, activity_end_hour):
    """
    Create a Meteomatics format DataFrame from filtered GRIB data.

    Args:
        filtered_df (pandas.DataFrame): Filtered GRIB data
        run_date (str): Run date in format "YYYY-MM-DD-HH"
        latitude (float): Target latitude
        longitude (float): Target longitude
        activity_start_date (str): Activity start date in format "YYYY-MM-DD"
        activity_start_hour (int): Activity start hour (0-23)
        activity_end_hour (int): Activity end hour (0-23)

    Returns:
        pandas.DataFrame: Meteomatics format DataFrame
    """
    import json
    from datetime import datetime
    import pandas as pd

    # Parse run date components
    run_dt = datetime.strptime(run_date, '%Y-%m-%d-%H')

    # Create weather data JSON for each hour in the activity period
    weather_data = []

    # Generate forecast data for each hour between activity_start_hour and activity_end_hour-1
    for hour in range(activity_start_hour, activity_end_hour):
        # Find matching data in filtered_df for this hour
        hour_str = f"{hour:02d}:00:00"

        # Look for data matching this hour in the filtered data
        matching_rows = filtered_df[
            filtered_df['valid_time_fr'].str.contains(f" {hour:02d}:", na=False)
        ]

        if not matching_rows.empty:
            # Use the precipitation value from the first matching row
            precipitation_value = matching_rows['precipitation'].iloc[0]
        else:
            # Default to 0.0 if no data found for this hour
            precipitation_value = 0.0

        # Parse activity start date for forecast date components
        activity_dt = datetime.strptime(activity_start_date, '%Y-%m-%d')

        weather_entry = {
            "forecast_hour": hour_str,
            "forecast_day": activity_dt.day,
            "forecast_month": activity_dt.month,
            "forecast_year": activity_dt.year,
            "value": float(precipitation_value)
        }
        weather_data.append(weather_entry)

    # Create the Meteomatics format DataFrame
    meteomatics_data = {
        'latitude': [latitude],
        'longitude': [longitude],
        'meteomatics_run_hour_utc': [run_dt.hour],
        'meteomatics_init_day': [run_dt.day],
        'meteomatics_init_month': [run_dt.month],
        'meteomatics_init_year': [run_dt.year],
        'weather_data': [json.dumps(weather_data)],
        'data_source': ['ECMWF'],
        'trip_day': [activity_start_date],
        'last_modified': [datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]]  # Format: 2024-08-31 18:15:15.000
    }

    meteomatics_df = pd.DataFrame(meteomatics_data)

    return meteomatics_df


def filter_grib_data_by_location_and_time(run_date, latitude, longitude, activity_start_date, activity_end_date, activity_start_hour, activity_end_hour, input_format='auto'):
    """
    Filter GRIB data from S3 processed CSV or Parquet by location and time period.

    Args:
        run_date (str): Run date in format "YYYY-MM-DD-HH"
        latitude (float): Target latitude (will be rounded to 1 decimal)
        longitude (float): Target longitude (will be rounded to 1 decimal)
        activity_start_date (str): Activity start date in format "YYYY-MM-DD"
        activity_end_date (str): Activity end date in format "YYYY-MM-DD"
        activity_start_hour (int): Activity start hour (0-23)
        activity_end_hour (int): Activity end hour (0-23)
        input_format (str): Input format - 'csv', 'parquet', or 'auto' (detect automatically) - DEFAULT: 'auto'

    Returns:
        pandas.DataFrame: Filtered data (original format)
        str: Path to output French CSV file
        pandas.DataFrame: Meteomatics format DataFrame with JSON weather data
    """
    from grib_processor import export_to_french_csv
    from datetime import datetime, timedelta
    import pandas as pd

    logger.info(f"Filtering GRIB data for location ({latitude}, {longitude}) and activity period")

    try:
        # Step 1: Round coordinates to 1 decimal place
        target_lat = round(latitude, 1)
        target_lon = round(longitude, 1)
        logger.info(f"Target coordinates rounded: ({target_lat}, {target_lon})")

        # Step 2: Create full activity start and end dates
        full_activity_start_date = datetime.strptime(f"{activity_start_date} {activity_start_hour:02d}:00", "%Y-%m-%d %H:%M")
        full_activity_end_date = datetime.strptime(f"{activity_end_date} {activity_end_hour:02d}:00", "%Y-%m-%d %H:%M")

        logger.info(f"Activity period: {full_activity_start_date} to {full_activity_end_date}")

        # Step 3: Find CSV or Parquet file in S3 bucket ecmwf-forecast-by-run-date
        upload_bucket = "ecmwf-forecast-by-run-date"
        file_prefix = f"ecmwf_{run_date.replace(':', '-')}"

        logger.info(f"Looking for file with prefix: {file_prefix} in bucket: {upload_bucket}")

        # Create S3 client for upload bucket
        upload_s3_client = boto3.client(
            's3',
            aws_access_key_id="********************",
            aws_secret_access_key="MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf",
            region_name='eu-west-3'
        )

        # List objects with the prefix
        response = upload_s3_client.list_objects_v2(Bucket=upload_bucket, Prefix=file_prefix)

        if 'Contents' not in response:
            raise FileNotFoundError(f"No files found with prefix '{file_prefix}' in bucket '{upload_bucket}'")

        # Find CSV and Parquet files
        csv_files = [obj['Key'] for obj in response['Contents'] if obj['Key'].endswith('.csv')]
        parquet_files = [obj['Key'] for obj in response['Contents'] if obj['Key'].endswith('.parquet')]

        # Determine which file to use based on input_format preference
        if input_format == 'parquet' or (input_format == 'auto' and parquet_files):
            if not parquet_files:
                raise FileNotFoundError(f"No Parquet files found with prefix '{file_prefix}' in bucket '{upload_bucket}'")

            if len(parquet_files) > 1:
                logger.warning(f"Multiple Parquet files found, using the first one: {parquet_files[0]}")

            data_file_key = parquet_files[0]
            file_format = 'parquet'
            logger.info(f"Found Parquet file: {data_file_key}")

        else:
            if not csv_files:
                raise FileNotFoundError(f"No CSV files found with prefix '{file_prefix}' in bucket '{upload_bucket}'")

            if len(csv_files) > 1:
                logger.warning(f"Multiple CSV files found, using the first one: {csv_files[0]}")

            data_file_key = csv_files[0]
            file_format = 'csv'
            logger.info(f"Found CSV file: {data_file_key}")

        # Step 4: Download and read the data file (CSV or Parquet)
        import tempfile

        if file_format == 'parquet':
            # Handle Parquet file
            with tempfile.NamedTemporaryFile(suffix=".parquet", delete=False) as tmp_file:
                tmp_path = tmp_file.name

            logger.info(f"📥 Downloading Parquet file from S3...")
            upload_s3_client.download_file(upload_bucket, data_file_key, tmp_path)
            logger.info(f"✅ Downloaded Parquet file to: {tmp_path}")

            # Read Parquet with column filtering and coordinate filtering for maximum performance
            logger.info(f"📊 Reading Parquet data with optimized filtering...")
            required_columns = ['latitude', 'longitude', 'valid_time_fr', 'precipitation']

            # Use Parquet's built-in filtering for coordinates (much faster!)
            target_lat = round(latitude, 1)
            target_lon = round(longitude, 1)

            try:
                # Try to use coordinate filtering directly in Parquet read
                df = pd.read_parquet(
                    tmp_path,
                    columns=required_columns,
                    filters=[
                        ('latitude', '>=', target_lat - 0.05),
                        ('latitude', '<=', target_lat + 0.05),
                        ('longitude', '>=', target_lon - 0.05),
                        ('longitude', '<=', target_lon + 0.05)
                    ]
                )
                logger.info(f"✅ Loaded Parquet data with coordinate pre-filtering: {len(df)} rows")
            except:
                # Fallback to reading all data if filtering fails
                df = pd.read_parquet(tmp_path, columns=required_columns)
                logger.info(f"✅ Loaded Parquet data (no pre-filtering): {len(df)} rows")

        else:
            # Handle CSV file
            with tempfile.NamedTemporaryFile(suffix=".csv", delete=False) as tmp_file:
                tmp_path = tmp_file.name

            logger.info(f"📥 Downloading CSV file from S3...")
            upload_s3_client.download_file(upload_bucket, data_file_key, tmp_path)
            logger.info(f"✅ Downloaded CSV file to: {tmp_path}")

            # Read French CSV format (semicolon separator, comma decimal)
            # Only read the columns we need for filtering and output
            logger.info(f"📊 Reading CSV data (optimized: only required columns)...")
            required_columns = ['latitude', 'longitude', 'valid_time_fr', 'precipitation']
            df = pd.read_csv(tmp_path, sep=';', decimal=',', encoding='utf-8-sig', usecols=required_columns)
            logger.info(f"✅ Loaded CSV data: {len(df)} rows, {len(df.columns)} columns")

        # Clean up temporary file
        os.remove(tmp_path)

        # Step 5: Filter by coordinates (if not already pre-filtered)
        if file_format == 'parquet' and len(df) < 1000:  # Likely already pre-filtered
            logger.info(f"📍 Parquet data appears pre-filtered by coordinates")
            # Still do exact matching for precision
            df['latitude_rounded'] = df['latitude'].round(1)
            df['longitude_rounded'] = df['longitude'].round(1)
            coord_filtered = df[
                (df['latitude_rounded'] == target_lat) &
                (df['longitude_rounded'] == target_lon)
            ].copy()
        else:
            logger.info(f"📍 Filtering by coordinates: lat={target_lat}, lon={target_lon}")
            # Round DataFrame coordinates to 1 decimal for matching
            df['latitude_rounded'] = df['latitude'].round(1)
            df['longitude_rounded'] = df['longitude'].round(1)

            coord_filtered = df[
                (df['latitude_rounded'] == target_lat) &
                (df['longitude_rounded'] == target_lon)
            ].copy()

        if coord_filtered.empty:
            available_coords = df[['latitude_rounded', 'longitude_rounded']].drop_duplicates()
            raise ValueError(f"No data found for coordinates ({target_lat}, {target_lon}). "
                           f"Available coordinates: {len(available_coords)} unique points. "
                           f"Sample: {available_coords.head().values.tolist()}")

        logger.info(f"✅ Coordinate filtering result: {len(coord_filtered)} rows")

        # Step 6: Filter by time period
        logger.info(f"⏰ Filtering by time period...")

        # Convert valid_time_fr to datetime (handle French format)
        if 'valid_time_fr' in coord_filtered.columns:
            # Parse French datetime format (DD/MM/YYYY HH:MM)
            coord_filtered['valid_time_parsed'] = pd.to_datetime(
                coord_filtered['valid_time_fr'],
                format='%d/%m/%Y %H:%M',
                errors='coerce'
            )
        else:
            raise ValueError("Column 'valid_time_fr' not found in the data")

        # Filter by activity period
        time_filtered = coord_filtered[
            (coord_filtered['valid_time_parsed'] >= full_activity_start_date) &
            (coord_filtered['valid_time_parsed'] < full_activity_end_date)
        ].copy()

        if time_filtered.empty:
            available_times = coord_filtered['valid_time_parsed'].dropna()
            time_range = f"{available_times.min()} to {available_times.max()}" if not available_times.empty else "No valid times"
            raise ValueError(f"No data found for activity period {full_activity_start_date} to {full_activity_end_date}. "
                           f"Available time range: {time_range}")

        logger.info(f"✅ Time filtering result: {len(time_filtered)} rows")

        # Step 7: Clean up and prepare final DataFrame
        # Remove temporary columns
        final_df = time_filtered.drop(['latitude_rounded', 'longitude_rounded', 'valid_time_parsed'], axis=1)

        # Step 8: Generate output filename
        logger.info(f"📁 Generating output filename...")
        start_date_str = activity_start_date.replace('-', '')
        end_date_str = activity_end_date.replace('-', '')
        output_filename = (f"filtered_data_lat{target_lat}_lon{target_lon}_"
                          f"{start_date_str}h{activity_start_hour:02d}_"
                          f"{end_date_str}h{activity_end_hour:02d}.csv")

        # Step 9: Export to French CSV
        logger.info(f"🇫🇷 Exporting to French CSV...")
        french_csv_file = export_to_french_csv(final_df, output_filename)

        # Step 10: Create Meteomatics format DataFrame
        logger.info(f"📊 Creating Meteomatics format DataFrame...")
        meteomatics_df = create_meteomatics_format_df(
            final_df, run_date, latitude, longitude,
            activity_start_date, activity_start_hour, activity_end_hour
        )

        logger.info(f"✅ Filtering completed successfully!")
        logger.info(f"📊 Final result: {len(final_df)} rows")
        logger.info(f"📊 Meteomatics format: {len(meteomatics_df)} rows")
        logger.info(f"📁 Output file: {french_csv_file}")

        return final_df, french_csv_file, meteomatics_df

    except Exception as e:
        logger.error(f"❌ Error filtering GRIB data: {e}")
        raise

if __name__ == "__main__":
    # 🧪 S3 CONNECTIVITY TEST
    print("🚀 Testing S3 Connectivity and Enhanced GRIB Processor Function")
    print("=" * 70)

    # Test S3 connectivity first
    print("\n🔄 Step 0: Testing S3 connectivity...")
    try:
        # List first 10 objects to test connectivity
        all_objects = list_s3_objects()
        if all_objects:
            print(f"✅ S3 connectivity successful! Found {len(all_objects)} objects in bucket")
            print("📋 First 10 objects:")
            for i, obj in enumerate(all_objects[:10]):
                print(f"   {i+1}. {obj}")
            if len(all_objects) > 10:
                print(f"   ... and {len(all_objects) - 10} more objects")
        else:
            print("⚠️ S3 bucket appears to be empty or inaccessible")
    except Exception as e:
        print(f"❌ S3 connectivity failed: {e}")
        print("Please check your AWS credentials and bucket configuration")

    print("\n" + "=" * 70)
    # 🧪 ENHANCED FUNCTION TEST
    print("🚀 Testing Enhanced S3 GRIB Processor Function")
    print("=" * 50)

    # Test parameters
    run_date = "2025-04-28-00"
    test_rows = 100000000000  # Small subset for quick testing

    print(f"📅 Run date: {run_date}")
    print(f"📊 Testing with {test_rows} rows")

    try:
        # Step 1: Get basic data from S3
        print("\n🔄 Step 1: Processing basic GRIB data from S3...")
        basic_df = process_s3_grib_files_for_run(run_date)
        print("test longueur")
        print(len(basic_df))
        print(basic_df)
        
        if basic_df.empty or 'variable_check' in basic_df.columns:
            print("❌ No valid basic data available")
        else:
            print(f"✅ Basic data loaded: {basic_df.shape[0]:,} rows")
            print(f"📋 Basic columns: {list(basic_df.columns)}")
            
            # Step 2: Test enhanced S3 processing and export function
            print(f"\n🔄 Step 2: Testing enhanced S3 processing and export function...")
            enhanced_df, csv_file = process_and_export_s3_grib_files(run_date, include_enhanced_columns=True)

            if csv_file:
                print(f"✅ Enhanced S3 processing completed!")
                print(f"📊 Enhanced data: {enhanced_df.shape}")
                print(f"📋 Enhanced columns: {list(enhanced_df.columns)}")
                print(f"📤 Data exported to: {csv_file}")
                print(f"📊 File size: {os.path.getsize(csv_file):,} bytes")

                # Step 3: Show sample data
                print("\n📋 Sample enhanced data:")
                if 'valid_time_fr' in enhanced_df.columns:
                    display_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation',
                                   'run_month', 'run_day', 'forecasted_hour']
                    available_cols = [col for col in display_cols if col in enhanced_df.columns]
                    print(enhanced_df[available_cols].head())
                else:
                    print(enhanced_df.head())

                # Step 4: Quick verification
                print("\n🔍 Quick Verification:")

                # Check precipitation stats if available
                if 'precipitation' in enhanced_df.columns:
                    precip_stats = enhanced_df['precipitation'].describe()
                    print(f"   📊 Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm")

                # Check timezone conversion if available
                if 'valid_time_fr' in enhanced_df.columns:
                    sample_utc = enhanced_df['valid_time'].iloc[0]
                    sample_paris = enhanced_df['valid_time_fr'].iloc[0]
                    print(f"   🌍 Timezone: {sample_utc} → {sample_paris}")

                # Check date components if available
                if 'run_year' in enhanced_df.columns:
                    sample_row = enhanced_df.iloc[0]
                    print(f"   📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}")
                    print(f"   📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}")

                # Check S3 metadata
                if 'min_date_upload' in enhanced_df.columns:
                    print(f"   📅 Upload date range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}")

                print("\n🎉 Enhanced S3 function test completed successfully!")
            else:
                print("❌ Enhanced S3 processing failed - no CSV file created")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
    traceback.print_exc()
