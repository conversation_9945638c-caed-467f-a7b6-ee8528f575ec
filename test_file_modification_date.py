#!/usr/bin/env python3
"""
Test script for the new file modification date column
"""

from grib_processor import process_single_grib_file, process_grib_files_for_run
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_file_modification_date():
    """Test the new file modification date functionality"""
    
    print("🔍 Testing File Modification Date Column")
    print("=" * 50)
    
    # Test 1: Single file processing
    print("📁 Test 1: Single File Processing")
    print("-" * 30)
    
    # Find a test file
    if os.path.exists("Archive"):
        files = [f for f in os.listdir("Archive") if not f.endswith('.idx')]
        if files:
            test_file = os.path.join("Archive", files[0])
            print(f"Testing with file: {test_file}")
            
            # Get file modification time directly
            import datetime
            mod_time = os.path.getmtime(test_file)
            expected_date = datetime.datetime.fromtimestamp(mod_time)
            print(f"Expected modification date: {expected_date}")
            
            # Process the file
            df, csv_file, validation = process_single_grib_file(test_file)
            
            if not df.empty:
                if 'file_modification_date' in df.columns:
                    actual_date = df['file_modification_date'].iloc[0]
                    print(f"✅ file_modification_date column found!")
                    print(f"Actual modification date: {actual_date}")
                    print(f"Match: {expected_date == actual_date}")
                else:
                    print("❌ file_modification_date column not found")
                    print(f"Available columns: {list(df.columns)}")
            else:
                print("❌ No data processed")
        else:
            print("❌ No files found in Archive")
    else:
        print("❌ Archive folder not found")
    
    # Test 2: Batch processing
    print(f"\n📊 Test 2: Batch Processing")
    print("-" * 30)
    
    run_date = "2025-04-28-00"
    print(f"Testing with run date: {run_date}")
    
    # Process a small subset for testing
    try:
        result = process_grib_files_for_run(run_date)
        
        if not result.empty:
            if 'file_modification_date' in result.columns:
                print(f"✅ file_modification_date column found in batch processing!")
                print(f"📊 Data shape: {result.shape}")
                
                # Show unique modification dates
                unique_dates = result[['file_name', 'file_modification_date']].drop_duplicates()
                print(f"📅 Unique file modification dates:")
                for _, row in unique_dates.head().iterrows():
                    print(f"   {row['file_name']}: {row['file_modification_date']}")
                
                # Show date range
                min_date = result['file_modification_date'].min()
                max_date = result['file_modification_date'].max()
                print(f"📊 Date range: {min_date} to {max_date}")
                
            else:
                print("❌ file_modification_date column not found in batch processing")
                print(f"Available columns: {list(result.columns)}")
        else:
            print("❌ No data processed in batch")
            
    except Exception as e:
        print(f"❌ Error in batch processing: {e}")
    
    print("\n🎉 File modification date test completed!")

if __name__ == "__main__":
    test_file_modification_date()
