{"cells": [{"cell_type": "code", "execution_count": 4, "id": "f26250e5", "metadata": {}, "outputs": [], "source": ["import os\n", "import bz2\n", "import xarray as xr\n", "\n", "def est_bz2(filepath):\n", "    \"\"\"Détecte si un fichier est un bzip2 grâce à sa signature magique.\"\"\"\n", "    with open(filepath, 'rb') as f:\n", "        signature = f.read(3)\n", "    return signature == b'BZh'\n", "\n", "def lire_fichier_ecmwf_auto(filepath):\n", "    # Vérifie si le fichier est compressé\n", "    is_compressed = est_bz2(filepath)\n", "\n", "    if is_compressed:\n", "        print(f\"📦 Le fichier '{filepath}' est compressé (bzip2)\")\n", "        with bz2.open(filepath, 'rb') as f:\n", "            contenu = f.read()\n", "\n", "        temp_grib = 'temp_decompressed.grib'\n", "        with open(temp_grib, 'wb') as temp:\n", "            temp.write(contenu)\n", "        chemin_grib = temp_grib\n", "    else:\n", "        print(f\"📂 Le fichier '{filepath}' est non compressé\")\n", "        chemin_grib = filepath\n", "\n", "    try:\n", "        ds = xr.open_dataset(chemin_grib, engine='cfgrib')\n", "        print(\"✅ Fichier GRIB chargé avec succès\")\n", "    except Exception as e:\n", "        print(f\"❌ Erreur de lecture GRIB : {e}\")\n", "        ds = None\n", "    finally:\n", "        if is_compressed and os.path.exists(temp_grib):\n", "            os.remove(temp_grib)\n", "\n", "    return ds, is_compressed\n"]}, {"cell_type": "code", "execution_count": 6, "id": "a8c9feb8", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'A1D04260000042620001'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m ds, est_compressé \u001b[38;5;241m=\u001b[39m \u001b[43mlire_fichier_ecmwf_auto\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mA1D04260000042620001\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m est_compressé:\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLe fichier original était compressé.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[1;32mIn[4], line 13\u001b[0m, in \u001b[0;36mlire_fichier_ecmwf_auto\u001b[1;34m(filepath)\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mlire_fichier_ecmwf_auto\u001b[39m(filepath):\n\u001b[0;32m     12\u001b[0m     \u001b[38;5;66;03m# Vérifie si le fichier est compressé\u001b[39;00m\n\u001b[1;32m---> 13\u001b[0m     is_compressed \u001b[38;5;241m=\u001b[39m \u001b[43mest_bz2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     15\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_compressed:\n\u001b[0;32m     16\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m📦 Le fichier \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfilepath\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m est compressé (bzip2)\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[1;32mIn[4], line 7\u001b[0m, in \u001b[0;36mest_bz2\u001b[1;34m(filepath)\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mest_bz2\u001b[39m(filepath):\n\u001b[0;32m      6\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Détecte si un fichier est un bzip2 grâce à sa signature magique.\"\"\"\u001b[39;00m\n\u001b[1;32m----> 7\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mfilepath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mrb\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m      8\u001b[0m         signature \u001b[38;5;241m=\u001b[39m f\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;241m3\u001b[39m)\n\u001b[0;32m      9\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m signature \u001b[38;5;241m==\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBZh\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\interactiveshell.py:324\u001b[0m, in \u001b[0;36m_modified_open\u001b[1;34m(file, *args, **kwargs)\u001b[0m\n\u001b[0;32m    317\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m file \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m}:\n\u001b[0;32m    318\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    319\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIPython won\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt let you open fd=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m by default \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    320\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    321\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myou can use builtins\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m open.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    322\u001b[0m     )\n\u001b[1;32m--> 324\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mio_open\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'A1D04260000042620001'"]}], "source": ["ds, est_compressé = lire_fichier_ecmwf_auto(\"A1D04260000042620001\")\n", "\n", "if est_compressé:\n", "    print(\"Le fichier original était compressé.\")\n", "else:\n", "    print(\"Le fichier original était déjà décompressé.\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "92967266", "metadata": {}, "outputs": [], "source": ["import os\n", "import bz2\n", "import xarray as xr\n", "import pandas as pd\n", "\n", "def est_bz2(filepath):\n", "    \"\"\"Détecte si un fichier est un bzip2 grâce à sa signature magique.\"\"\"\n", "    with open(filepath, 'rb') as f:\n", "        signature = f.read(3)\n", "    return signature == b'BZh'\n", "\n", "def lire_fichier_ecmwf_auto(filepath):\n", "    # Vérifie si le fichier est compressé\n", "    is_compressed = est_bz2(filepath)\n", "\n", "    if is_compressed:\n", "        print(f\"📦 Le fichier '{filepath}' est compressé (bzip2)\")\n", "        with bz2.open(filepath, 'rb') as f:\n", "            contenu = f.read()\n", "\n", "        temp_grib = 'temp_decompressed.grib'\n", "        with open(temp_grib, 'wb') as temp:\n", "            temp.write(contenu)\n", "        chemin_grib = temp_grib\n", "    else:\n", "        print(f\"📂 Le fichier '{filepath}' est non compressé\")\n", "        chemin_grib = filepath\n", "\n", "    try:\n", "        # Chargement du fichier GRIB avec xarray\n", "        ds = xr.open_dataset(chemin_grib, engine='cfgrib')\n", "        print(\"✅ Fichier GRIB chargé avec succès\")\n", "    except Exception as e:\n", "        print(f\"❌ Erreur de lecture GRIB : {e}\")\n", "        ds = None\n", "    finally:\n", "        if is_compressed and os.path.exists(temp_grib):\n", "            os.remove(temp_grib)\n", "\n", "    return ds, is_compressed\n", "\n", "def convertir_en_dataframe(ds):\n", "    \"\"\"Convertit un dataset xarray en dataframe pandas.\"\"\"\n", "    if ds is not None:\n", "        df = ds.to_dataframe()\n", "        print(\"\\nVoici un aperçu du DataFrame :\")\n", "        print(df.head())  # Affiche les 5 premières lignes du DataFrame\n", "        return df\n", "    else:\n", "        print(\"❌ Le dataset est vide.\")\n", "        return None\n", "\n", "def exporter_en_csv(df, chemin_sortie=\"data.csv\"):\n", "    \"\"\"Exporte le DataFrame en fichier CSV.\"\"\"\n", "    if df is not None:\n", "        df.to_csv(chemin_sortie)\n", "        print(f\"✅ DataFrame exporté en CSV à l'emplacement {chemin_sortie}\")\n", "    else:\n", "        print(\"❌ Impossible d'exporter un DataFrame vide.\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c0af8e4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📂 Le fichier 'Archive/A1D04260000042602001' est non compressé\n", "✅ Fichier GRIB chargé avec succès\n", "\n", "Voici un aperçu du DataFrame :\n", "                    number       time            step  surface  \\\n", "latitude longitude                                               \n", "51.4     -5.9            0 2025-04-26 0 days 02:00:00      0.0   \n", "         -5.8            0 2025-04-26 0 days 02:00:00      0.0   \n", "         -5.7            0 2025-04-26 0 days 02:00:00      0.0   \n", "         -5.6            0 2025-04-26 0 days 02:00:00      0.0   \n", "         -5.5            0 2025-04-26 0 days 02:00:00      0.0   \n", "\n", "                            valid_time        tp  \n", "latitude longitude                                \n", "51.4     -5.9      2025-04-26 02:00:00  0.000246  \n", "         -5.8      2025-04-26 02:00:00  0.000382  \n", "         -5.7      2025-04-26 02:00:00  0.000439  \n", "         -5.6      2025-04-26 02:00:00  0.000609  \n", "         -5.5      2025-04-26 02:00:00  0.000884  \n"]}], "source": ["# Charger le fichier et obtenir le dataset xarray\n", "ds, est_compressé = lire_fichier_ecmwf_auto(\"Archive/A1D04260000042602001\")\n", "# A1D04260000042602001\n", "# Convertir en DataFrame pandas\n", "df = convertir_en_dataframe(ds)\n", "\n", "# Exporter le DataFrame en CSV\n", "df.to_csv(\"A1D04260000042602001.csv\", sep=';', decimal=',', index=True, encoding='utf-8')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "fcaf9c1f", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "def trouver_fichier(date, heure, dossier=\"Archive\"):\n", "    \"\"\"\n", "    <PERSON><PERSON><PERSON> le fichier correspondant à la date et l'heure dans le dossier.\n", "    \"\"\"\n", "    # Format attendu dans le nom du fichier : MMDDHHmmddhh\n", "    mois = f\"{date.month:02d}\"\n", "    jour = f\"{date.day:02d}\"\n", "    heure = f\"{heure:02d}\"\n", "    minute = \"00\"  # souvent 00\n", "    pattern = f\"{mois}{jour}{heure}{minute}\"\n", "\n", "    for fichier in os.listdir(dossier):\n", "        if pattern in fichier:\n", "            return os.path.join(dossier, fichier)\n", "\n", "    raise FileNotFoundError(f\"<PERSON><PERSON>er pour {date} {heure}h non trouvé dans {dossier}.\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}