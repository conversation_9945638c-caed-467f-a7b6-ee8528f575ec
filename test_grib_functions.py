#!/usr/bin/env python3
"""
GRIB Processor Testing Script

This script provides step-by-step testing of the GRIB processor functions.
Run each section individually or the entire script.
"""

from grib_processor import (
    parse_ecmwf_filename,
    get_files_for_run_date,
    check_forecast_files_availability,
    process_grib_files_for_run,
    process_and_export_grib_files,
    process_single_grib_file
)
import pandas as pd
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_filename_parsing():
    """Test ECMWF filename parsing"""
    print("=" * 60)
    print("1. TESTING ECMWF FILENAME PARSING")
    print("=" * 60)
    
    test_filenames = [
        "A1D04260000042602001",  # Run: Apr 26 00:00, Forecast: Apr 26 02:00
        "A1S04261800042618011",  # Run: Apr 26 18:00, Forecast: Apr 26 18:01
        "A1D04270000042700011",  # Run: Apr 27 00:00, Forecast: Apr 27 00:01
    ]
    
    for filename in test_filenames:
        parsed = parse_ecmwf_filename(filename)
        if parsed:
            print(f"📁 {filename}:")
            print(f"   🏃 Run: {parsed['run_month']}/{parsed['run_day']} at {parsed['run_hour']}:{parsed['run_minute']}")
            print(f"   🔮 Forecast: {parsed['forecast_month']}/{parsed['forecast_day']} at {parsed['forecast_hour']}:{parsed['forecast_minute']}")
            print(f"   📡 Stream: {parsed['dissemination_stream']}{parsed['stream_indicator']}")
        else:
            print(f"❌ Could not parse: {filename}")
        print()

def test_file_discovery():
    """Test file discovery for run dates"""
    print("=" * 60)
    print("2. TESTING FILE DISCOVERY")
    print("=" * 60)
    
    test_run_dates = ["2025-04-26-00", "2025-04-26-12", "2025-04-27-00"]
    
    for run_date in test_run_dates:
        try:
            files = get_files_for_run_date(run_date)
            print(f"📅 {run_date}: Found {len(files)} files")
            
            if files:
                print("   Sample files:")
                for i, filepath in enumerate(files[:3]):
                    filename = os.path.basename(filepath)
                    parsed = parse_ecmwf_filename(filename)
                    if parsed:
                        print(f"   {i+1}. {filename} → Forecast: {parsed['forecast_month']}/{parsed['forecast_day']} {parsed['forecast_hour']}:{parsed['forecast_minute']}")
                if len(files) > 3:
                    print(f"   ... and {len(files) - 3} more files")
        except Exception as e:
            print(f"📅 {run_date}: Error - {e}")
        print()

def test_availability_check():
    """Test forecast file availability check"""
    print("=" * 60)
    print("3. TESTING FORECAST AVAILABILITY CHECK")
    print("=" * 60)
    
    test_run_date = "2025-04-26-00"
    max_hours = 12  # Check first 12 hours for faster testing
    
    try:
        print(f"🔍 Checking availability for {test_run_date} (first {max_hours} hours)")
        
        availability_df = check_forecast_files_availability(test_run_date, max_forecast_hours=max_hours)
        
        if not availability_df.empty:
            total_files = len(availability_df)
            present_files = availability_df['is_present'].sum()
            missing_files = total_files - present_files
            
            print(f"📊 Total expected files: {total_files}")
            print(f"📊 Files present: {present_files}")
            print(f"📊 Files missing: {missing_files}")
            print(f"📊 Availability rate: {(present_files/total_files)*100:.1f}%")
            
            print("\n📋 Sample results:")
            print(availability_df.head())
            
            # Show missing files
            missing = availability_df[availability_df['is_present'] == 0]
            if not missing.empty:
                print(f"\n❌ Missing files (first 3):")
                for _, row in missing.head(3).iterrows():
                    print(f"   {row['file_name']} → {row['forecasted_date']} {row['forecasted_hour']:02d}:00")
        else:
            print("❌ No availability data returned")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    print()

def test_data_processing():
    """Test complete data processing"""
    print("=" * 60)
    print("4. TESTING DATA PROCESSING")
    print("=" * 60)
    
    test_run_date = "2025-04-26-00"
    
    try:
        print(f"🚀 Processing all files for {test_run_date}")
        
        result_df = process_grib_files_for_run(test_run_date)
        
        if not result_df.empty:
            print(f"✅ Processing completed!")
            print(f"📊 Result shape: {result_df.shape}")
            print(f"📋 Columns: {list(result_df.columns)}")
            
            # Check if it's an error table or data table
            if 'variable_check' in result_df.columns:
                print("\n❌ Validation errors found:")
                print(result_df)
            else:
                print(f"\n✅ Data compilation successful!")
                print(f"📊 Total rows: {len(result_df):,}")
                print(f"📊 Unique files: {result_df['file_name'].nunique()}")
                
                print("\n📋 Sample data:")
                print(result_df.head())
                
                print("\n📊 Data summary:")
                print(f"   Latitude range: {result_df['latitude'].min():.2f} to {result_df['latitude'].max():.2f}")
                print(f"   Longitude range: {result_df['longitude'].min():.2f} to {result_df['longitude'].max():.2f}")
                print(f"   TP range: {result_df['tp'].min():.6f} to {result_df['tp'].max():.6f}")
        else:
            print("❌ No data returned from processing")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    print()

def test_csv_export():
    """Test CSV export functionality"""
    print("=" * 60)
    print("5. TESTING CSV EXPORT")
    print("=" * 60)
    
    test_run_date = "2025-04-26-00"
    
    # Test 1: Export availability check
    print("📤 Testing availability check export:")
    try:
        result, csv_file = process_and_export_grib_files(
            test_run_date, 
            check_availability=True, 
            max_forecast_hours=6  # Small number for testing
        )
        
        if csv_file:
            print(f"✅ Availability exported to: {csv_file}")
            print(f"📊 File size: {os.path.getsize(csv_file)} bytes")
            print(f"📊 Data shape: {result.shape}")
        else:
            print("❌ No CSV file created")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()
    
    # Test 2: Export compiled data
    print("📤 Testing data compilation export:")
    try:
        result, csv_file = process_and_export_grib_files(test_run_date)
        
        if csv_file:
            print(f"✅ Data exported to: {csv_file}")
            print(f"📊 File size: {os.path.getsize(csv_file):,} bytes")
            print(f"📊 Data shape: {result.shape}")
            
            if 'variable_check' not in result.columns and 'is_present' not in result.columns:
                print(f"📊 Files processed: {result['file_name'].nunique()}")
                print(f"📊 Total rows: {len(result):,}")
        else:
            print("❌ No CSV file created")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    print()

def test_single_file():
    """Test single file processing"""
    print("=" * 60)
    print("6. TESTING SINGLE FILE PROCESSING")
    print("=" * 60)
    
    if os.path.exists('Archive'):
        files = [f for f in os.listdir('Archive') if not f.endswith('.idx') and not f.startswith('.')]
        if files:
            test_file = os.path.join('Archive', files[0])
            
            print(f"🔍 Testing single file: {files[0]}")
            
            try:
                df, csv_file, validation = process_single_grib_file(test_file)
                
                print(f"📁 File: {validation['file_name']}")
                print(f"✅ Variables check: {'PASSED' if validation['variable_check_passed'] else 'FAILED'}")
                print(f"✅ Row count check: {'PASSED' if validation['row_check_passed'] else 'FAILED'}")
                print(f"📊 Dataset loaded: {'YES' if validation['dataset_loaded'] else 'NO'}")
                
                if csv_file:
                    print(f"\n📤 Single file exported to: {csv_file}")
                    print(f"📊 File size: {os.path.getsize(csv_file):,} bytes")
                    print(f"📊 Data shape: {df.shape}")
                    
                    if not df.empty:
                        print("\n📋 Sample data:")
                        print(df.head())
                        
                        print("\n📊 Data summary:")
                        print(f"   Latitude range: {df['latitude'].min():.2f} to {df['latitude'].max():.2f}")
                        print(f"   Longitude range: {df['longitude'].min():.2f} to {df['longitude'].max():.2f}")
                        print(f"   TP range: {df['tp'].min():.6f} to {df['tp'].max():.6f}")
                        if 'valid_time' in df.columns:
                            print(f"   Valid time: {df['valid_time'].iloc[0]}")
                else:
                    print("❌ No CSV file created")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        else:
            print("❌ No files found in Archive folder")
    else:
        print("❌ Archive folder not found")
    print()

def main():
    """Run all tests"""
    print("🧪 GRIB PROCESSOR TESTING SUITE")
    print("=" * 60)
    print(f"📁 Current directory: {os.getcwd()}")
    print(f"📂 Archive folder exists: {os.path.exists('Archive')}")
    print()
    
    # Run all tests
    test_filename_parsing()
    test_file_discovery()
    test_availability_check()
    test_data_processing()
    test_csv_export()
    test_single_file()
    
    print("=" * 60)
    print("🎉 ALL TESTS COMPLETED!")
    print("=" * 60)
    print("Check the generated CSV files for results.")
    print("Modify the test parameters above to match your specific data.")

if __name__ == "__main__":
    main()
