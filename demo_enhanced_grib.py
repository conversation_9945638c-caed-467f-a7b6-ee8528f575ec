#!/usr/bin/env python3
"""
Demo script showing the enhanced GRIB processor functionality
"""

from grib_processor import process_and_export_grib_files
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO)

def demo_enhanced_functionality():
    """Demonstrate the enhanced GRIB processor functionality"""
    
    print("🚀 Enhanced GRIB Processor Demo")
    print("=" * 50)
    
    run_date = "2025-04-28-00"
    print(f"📅 Processing run date: {run_date}")
    
    # Demo 1: Basic processing (without enhanced columns)
    print("\n📊 Demo 1: Basic Processing (Original)")
    print("-" * 40)
    
    result_basic, csv_basic = process_and_export_grib_files(
        run_date, 
        include_enhanced_columns=False
    )
    
    if csv_basic:
        print(f"✅ Basic data exported to: {csv_basic}")
        print(f"📊 Data shape: {result_basic.shape}")
        print(f"📋 Basic columns: {list(result_basic.columns)}")
        print(f"📁 File size: {os.path.getsize(csv_basic):,} bytes")
    
    # Demo 2: Enhanced processing (with all enhanced columns)
    print("\n🌟 Demo 2: Enhanced Processing (NEW!)")
    print("-" * 40)
    print("Adding:")
    print("  ✅ Decumulated precipitation (rounded to 1 decimal)")
    print("  ✅ Paris timezone conversion (valid_time_fr)")
    print("  ✅ Run date components (run_year, run_month, run_day, run_hour)")
    print("  ✅ Forecast date components (forecasted_year, forecasted_month, forecasted_day, forecasted_hour)")
    
    result_enhanced, csv_enhanced = process_and_export_grib_files(
        run_date, 
        include_enhanced_columns=True
    )
    
    if csv_enhanced:
        print(f"\n✅ Enhanced data exported to: {csv_enhanced}")
        print(f"📊 Data shape: {result_enhanced.shape}")
        print(f"📋 Enhanced columns: {list(result_enhanced.columns)}")
        print(f"📁 File size: {os.path.getsize(csv_enhanced):,} bytes")
        
        # Show the difference
        basic_cols = set(result_basic.columns)
        enhanced_cols = set(result_enhanced.columns)
        new_cols = enhanced_cols - basic_cols
        print(f"🆕 New columns added: {list(new_cols)}")
        
        # Show sample enhanced data
        print("\n📋 Sample enhanced data:")
        display_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation', 
                       'run_month', 'run_day', 'forecasted_hour']
        available_cols = [col for col in display_cols if col in result_enhanced.columns]
        print(result_enhanced[available_cols].head())
        
        # Show precipitation stats
        if 'precipitation' in result_enhanced.columns:
            precip_stats = result_enhanced['precipitation'].describe()
            print(f"\n📊 Precipitation statistics:")
            print(f"   Min: {precip_stats['min']:.1f} mm")
            print(f"   Max: {precip_stats['max']:.1f} mm")
            print(f"   Mean: {precip_stats['mean']:.1f} mm")
        
        # Show timezone conversion example
        if 'valid_time_fr' in result_enhanced.columns:
            sample_utc = result_enhanced['valid_time'].iloc[0]
            sample_paris = result_enhanced['valid_time_fr'].iloc[0]
            print(f"\n🌍 Timezone conversion example:")
            print(f"   UTC: {sample_utc}")
            print(f"   Paris: {sample_paris}")
        
        # Show date components
        if 'run_year' in result_enhanced.columns:
            sample_row = result_enhanced.iloc[0]
            print(f"\n📅 Date components example:")
            print(f"   Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d} {sample_row['run_hour']:02d}:00")
            print(f"   Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d} {sample_row['forecasted_hour']:02d}:00")
    
    print("\n🎉 Demo completed!")
    print("=" * 50)
    print("💡 Key Benefits:")
    print("   ✅ Automatic decumulation of precipitation data")
    print("   ✅ Timezone conversion for local analysis")
    print("   ✅ Easy date filtering with separate components")
    print("   ✅ Ready-to-use data for analysis and visualization")
    print("   ✅ Backward compatible (can disable with include_enhanced_columns=False)")

if __name__ == "__main__":
    demo_enhanced_functionality()
