# Import the S3 GRIB processor and required modules
from grib_processor_S3 import (
    list_s3_objects,
    get_s3_files_for_run_date,
    process_and_export_s3_grib_files,
    get_latest_available_run_date,
    get_latest_available_source_run_date,
    filter_grib_data_by_location_and_time_bulk,
    check_run_date_exists,
    bucket_name
)
import pandas as pd
import os
from datetime import datetime

print("✅ Imports successful!")
print(f"📦 S3 Bucket: {bucket_name}")
print(f"🔧 Environment ready for testing")
print(f"\n💡 Next: Configure test parameters in the cell below")

print("🔄 Testing S3 connectivity...")

# List objects in S3 bucket
objects = list_s3_objects()

if objects:
    print(f"✅ S3 connectivity successful!")
    print(f"📊 Found {len(objects)} objects in bucket")
    print("\n📋 First 10 objects:")
    for i, obj in enumerate(objects[:10]):
        print(f"   {i+1:2d}. {obj}")
else:
    print("❌ No objects found in S3 bucket")

# Configure the test run date here
# Format: YYYY-MM-DD-HH (e.g., "2025-04-28-00")
test_run_date = "2025-06-20-00"  # Change this to test different dates

print(f"📅 Test configuration:")
print(f"   Run date: {test_run_date}")
print(f"   Enhanced features: Enabled")
print(f"   Coordinate rounding: 1 decimal place")
print(f"   Date filtering: Next 2 days only")
print(f"\n✅ Configuration set! Ready to run tests.")

# Test file discovery for the configured run date
if 'test_run_date' not in locals():
    print("⚠️ Please run the configuration cell above first!")
else:
    print(f"🔍 Searching for files with run date: {test_run_date}")
    
    matching_files = get_s3_files_for_run_date(test_run_date)
    
    if matching_files:
        print(f"✅ Found {len(matching_files)} matching files")
        print("\n📋 Files found:")
        for i, file_key in enumerate(matching_files[:5]):
            print(f"   {i+1}. {file_key}")
        if len(matching_files) > 5:
            print(f"   ... and {len(matching_files) - 5} more files")
    else:
        print(f"❌ No files found for run date: {test_run_date}")
        print("💡 Try a different run date in the configuration cell above")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features - test both CSV and Parquet formats
    print(f"\n🔄 Testing CSV format first...")
    # enhanced_df_csv, csv_file = process_and_export_s3_grib_files(
    #     run_date=test_run_date,
    #     include_enhanced_columns=True,
    #     upload_to_s3_bucket=True,
    #     output_format='csv'
    # )
    
    print(f"\n🔄 Testing Parquet format...")
    enhanced_df_parquet, parquet_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        upload_locally=True,
        output_format_locally='csv',
        include_enhanced_columns=True,
        upload_to_s3_bucket=True,
        output_format='parquet'
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    print(processing_time)
    
    
else:
    print("⚠️ No files available for processing test")

# Test the new get_latest_available_source_run_date function
from grib_processor_S3 import get_latest_available_source_run_date
from datetime import datetime, timedelta


current_time = datetime.now()
print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

latest_source_run = get_latest_available_source_run_date(current_time)

enhanced_df_parquet, parquet_file = process_and_export_s3_grib_files(
    run_date=latest_source_run,
    upload_locally=False,
    output_format_locally='csv',
    include_enhanced_columns=True,
    upload_to_s3_bucket=True,
    output_format='parquet'
    )

# Quick data analysis
if 'enhanced_df' in locals() and not enhanced_df.empty:
    print("📊 Quick Data Analysis")
    print("=" * 30)
    
    # Basic statistics
    print(f"Total rows: {len(enhanced_df):,}")
    print(f"Total files: {enhanced_df['file_name'].nunique()}")
    print(f"Geographic coverage (rounded to 1 decimal):")
    print(f"  Latitude: {enhanced_df['latitude'].min():.1f} to {enhanced_df['latitude'].max():.1f}")
    print(f"  Longitude: {enhanced_df['longitude'].min():.1f} to {enhanced_df['longitude'].max():.1f}")
    
    # Date filtering verification
    if 'forecasted_year' in enhanced_df.columns:
        unique_dates = enhanced_df[['forecasted_year', 'forecasted_month', 'forecasted_day']].drop_duplicates()
        print(f"\nForecast dates (filtered to next 2 days):")
        for _, row in unique_dates.iterrows():
            print(f"  {int(row['forecasted_year'])}-{int(row['forecasted_month']):02d}-{int(row['forecasted_day']):02d}")
    
    # Precipitation analysis
    if 'precipitation' in enhanced_df.columns:
        precip_stats = enhanced_df['precipitation'].describe()
        print(f"\nPrecipitation (mm):")
        print(f"  Min: {precip_stats['min']:.1f}")
        print(f"  Max: {precip_stats['max']:.1f}")
        print(f"  Mean: {precip_stats['mean']:.2f}")
        print(f"  Non-zero values: {(enhanced_df['precipitation'] > 0).sum():,}")
    
    # S3 metadata
    if 'min_date_upload' in enhanced_df.columns:
        print(f"\nS3 Upload metadata:")
        print(f"  Upload range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}")
    
    print(f"\n✅ Data analysis completed!")
    print(f"✅ Coordinates rounded to 1 decimal place")
    print(f"✅ Data filtered to next 2 days only")
else:
    print("⚠️ No data available for analysis")

# Test the location and time filtering function
from grib_processor_S3 import filter_grib_data_by_location_and_time

print("🎯 Testing Location and Time Filtering Function")
print("=" * 50)

# Test parameters
test_run_date = "2025-06-17-06"  # Must match a processed file in S3
test_latitude = 45.1             # Target latitude
test_longitude = 2.2             # Target longitude
test_activity_start_date = "2025-06-17"  # Activity start date
test_activity_end_date = "2025-06-17"    # Activity end date
test_activity_start_hour = 17             # Activity start hour (6 AM)
test_activity_end_hour = 17              # Activity end hour (6 PM)

print(f"📅 Run date: {test_run_date}")
print(f"📍 Target location: ({test_latitude}, {test_longitude})")
print(f"⏰ Activity period: {test_activity_start_date} {test_activity_start_hour:02d}:00 to {test_activity_end_date} {test_activity_end_hour:02d}:00")

try:
    # Call the filtering function (now returns 3 outputs including Meteomatics format)
    filtered_df, output_csv, meteomatics_df = filter_grib_data_by_location_and_time(
        run_date=test_run_date,
        latitude=test_latitude,
        longitude=test_longitude,
        activity_start_date=test_activity_start_date,
        activity_end_date=test_activity_end_date,
        activity_start_hour=test_activity_start_hour,
        activity_end_hour=test_activity_end_hour,
        input_format='auto'
    )
    
    if not filtered_df.empty and output_csv:
        print(f"\n✅ Filtering successful!")
        print(f"📊 Filtered data shape: {filtered_df.shape}")
        print(f"📁 Output file: {output_csv}")
        
        # Display sample of filtered data
        print(f"\n📋 Sample of filtered data:")
        print(filtered_df.head())
        
        # Show time range in filtered data
        if 'valid_time_fr' in filtered_df.columns:
            print(f"\n⏰ Time range in filtered data:")
            print(f"   First time: {filtered_df['valid_time_fr'].iloc[0]}")
            print(f"   Last time: {filtered_df['valid_time_fr'].iloc[-1]}")
        
        # Show precipitation statistics
        if 'precipitation' in filtered_df.columns:
            precip_stats = filtered_df['precipitation'].describe()
            print(f"\n🌧️ Precipitation statistics (mm):")
            print(f"   Min: {precip_stats['min']:.1f} mm")
            print(f"   Max: {precip_stats['max']:.1f} mm")
            print(f"   Mean: {precip_stats['mean']:.1f} mm")
            print(f"   Non-zero values: {(filtered_df['precipitation'] > 0).sum()}")
        
        # Verify file format
        if os.path.exists(output_csv):
            file_size = os.path.getsize(output_csv) / 1024  # KB
            print(f"\n📄 Output file details:")
            print(f"   File size: {file_size:.1f} KB")
            print(f"   Format: French CSV (semicolon separator, comma decimal)")
            
            # Show first few lines of CSV to verify format
            with open(output_csv, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()[:3]
            print(f"   Header: {lines[0].strip()}")
            if len(lines) > 1:
                print(f"   Sample: {lines[1].strip()}")
        
        print(f"\n🎉 Location and time filtering test PASSED!")
        
    else:
        print(f"❌ Filtering failed - no data returned")
        
except FileNotFoundError as e:
    print(f"❌ File not found: {e}")
    print(f"💡 Make sure you have run the main processing first to create the CSV file in S3")
    
except ValueError as e:
    print(f"❌ Data filtering error: {e}")
    print(f"💡 Try different coordinates or time period")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()

# Test the new get_latest_available_run_date function
from grib_processor_S3 import get_latest_available_run_date
from datetime import datetime, timedelta

print("🔍 Testing Latest Available Run Date Function")
print("=" * 50)

# Test 1: Current time
print("\n📅 Test 1: Using current date/time")
current_time = datetime.now()
print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

latest_run = get_latest_available_run_date(current_time)
if latest_run:
    print(f"✅ Latest available run date: {latest_run}")
else:
    print(f"❌ No run date found")

# Test 2: Specific past date
print("\n📅 Test 2: Using specific past date")
past_date = datetime(2025, 6, 20, 7, 0, 0)
print(f"Test time: {past_date.strftime('%Y-%m-%d %H:%M:%S')}")

latest_run_past = get_latest_available_run_date(past_date)
if latest_run_past:
    print(f"✅ Latest available run date: {latest_run_past}")
else:
    print(f"❌ No run date found")


# Test the new get_latest_available_source_run_date function
from grib_processor_S3 import get_latest_available_source_run_date
from datetime import datetime, timedelta

# Test 1: Current time
print("\n📅 Test 1: Using current date/time")
current_time = datetime.now()
print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

latest_source_run = get_latest_available_source_run_date(current_time)
if latest_source_run:
    print(f"✅ Latest available source run date: {latest_source_run}")
else:
    print(f"❌ No source run date found")


# Test 3: String format input
print("\n📅 Test 3: Using string format input")
date_string = "2025-06-19 12:00:00"
print(f"Test time string: {date_string}")

latest_source_run_string = get_latest_available_source_run_date(date_string)
if latest_source_run_string:
    print(f"✅ Latest available source run date: {latest_source_run_string}")
else:
    print(f"❌ No source run date found")



###### RUN FOR AN ACTIVITY
from grib_processor_S3 import get_latest_available_run_date
from datetime import datetime, timedelta
from grib_processor_S3 import filter_grib_data_by_location_and_time


test_run_date = get_latest_available_run_date(datetime(2025, 6, 20, 20, 0, 0))  # Date et heure de début de l'activité
# test_run_date = get_latest_available_run_date(datetime.now())  # Must match a processed file in S3
print(test_run_date)

print(f"📅 Run date: {test_run_date}")
print(f"📍 Target location: ({test_latitude}, {test_longitude})")
print(f"⏰ Activity period: {test_activity_start_date} {test_activity_start_hour:02d}:00 to {test_activity_end_date} {test_activity_end_hour:02d}:00")


# Call the filtering function (now returns 3 outputs including Meteomatics format)
filtered_df, output_csv, meteomatics_df = filter_grib_data_by_location_and_time(
    run_date=test_run_date,
    latitude=45.1,
    longitude=2.2,
    activity_start_date="2025-06-20" ,
    activity_end_date="2025-06-20",
    activity_start_hour=20,
    activity_end_hour=23,
    input_format='auto'
)

# Display Meteomatics format results
print(f"\n📋 Meteomatics Format DataFrame:")
print(meteomatics_df)

meteomatics_df.to_csv('format_meteomatics.csv', index=False)


# Test the new bulk filtering function
from grib_processor_S3 import filter_grib_data_by_location_and_time_bulk
import json
from grib_processor_S3 import get_latest_available_run_date
from datetime import datetime, timedelta
from grib_processor_S3 import filter_grib_data_by_location_and_time

# Define multiple policies for bulk processing
test_policies = [
    {
        "policy_id": "POLICY_001",
        "latitude": 45.1,
        "longitude": 2.2,
        "activity_start_date": "2025-06-20",
        "activity_end_date": "2025-06-20",
        "activity_start_hour": 8,
        "activity_end_hour": 12
    },
    {
        "policy_id": "POLICY_002",
        "latitude": 46.5,
        "longitude": 3.1,
        "activity_start_date": "2025-06-20",
        "activity_end_date": "2025-06-20",
        "activity_start_hour": 14,
        "activity_end_hour": 18
    },
    {
        "policy_id": "POLICY_003",
        "latitude": 44.8,
        "longitude": 1.9,
        "activity_start_date": "2025-06-20",
        "activity_end_date": "2025-06-20",
        "activity_start_hour": 20,
        "activity_end_hour": 23
    }
]


test_run_date = get_latest_available_run_date(datetime(2025, 6, 20, 20, 0, 0))  # Date et heure de début de l'activité
# test_run_date = get_latest_available_run_date(datetime.now())  # Must match a processed file in S3

print(f"📅 Run date: {test_run_date}")


try:
    print(f"\n🚀 Calling bulk filtering function...")
    
    # Call the bulk filtering function
    combined_filtered_df, combined_csv_file, combined_meteomatics_df = filter_grib_data_by_location_and_time_bulk(
        run_date=test_run_date,
        policies=test_policies,
        input_format='auto'
    )
    
    print(f"\n✅ Bulk filtering successful!")
    print(f"📁 Combined output file: {combined_csv_file}")
    
    # Show policy distribution in filtered data
    print(f"\n📋 Policy distribution in filtered data:")
    policy_counts = combined_filtered_df['policy_id'].value_counts()
    for policy_id, count in policy_counts.items():
        print(f"   {policy_id}: {count} rows")
    
    # Show meteomatics format results
    print(f"\n📋 Combined Meteomatics Format DataFrame:")
    print(combined_meteomatics_df[['policy_id', 'latitude', 'longitude', 'meteomatics_run_hour_utc', 
                                   'data_source', 'trip_day']].head())
    
    # Show weather data for first policy
    if not combined_meteomatics_df.empty:
        first_policy_weather = combined_meteomatics_df.iloc[0]
        weather_json = first_policy_weather['weather_data']
        weather_data = json.loads(weather_json)
    
except Exception as e:
    print(f"❌ Bulk filtering failed: {e}")
    import traceback
    traceback.print_exc()

combined_meteomatics_df.to_csv('format_meteomatics_bulk.csv', index=False)

# Test the structure of the new Meteomatics format output
from grib_processor_S3 import create_meteomatics_format_df
import pandas as pd
import json
from datetime import datetime

print("🌤️ Testing Meteomatics Format Structure")
print("=" * 45)

# Create a sample filtered DataFrame to test the format function
sample_data = {
    'latitude': [45.1, 45.1, 45.1, 45.1],
    'longitude': [2.2, 2.2, 2.2, 2.2],
    'valid_time_fr': ['20/06/2025 08:00', '20/06/2025 09:00', '20/06/2025 10:00', '20/06/2025 11:00'],
    'precipitation': [0.5, 1.2, 0.0, 2.1]
}
sample_filtered_df = pd.DataFrame(sample_data)

print("📋 Sample filtered data:")
print(sample_filtered_df)

# Test the Meteomatics format creation
test_run_date = "2025-06-20-06"
test_latitude = 45.1
test_longitude = 2.2
test_activity_start_date = "2025-06-20"
test_activity_start_hour = 8
test_activity_end_hour = 12

print(f"\n🔄 Creating Meteomatics format with parameters:")
print(f"   Run date: {test_run_date}")
print(f"   Location: ({test_latitude}, {test_longitude})")
print(f"   Activity: {test_activity_start_date} {test_activity_start_hour:02d}:00 to {test_activity_end_hour:02d}:00")

meteomatics_df = create_meteomatics_format_df(
    sample_filtered_df, test_run_date, test_latitude, test_longitude,
    test_activity_start_date, test_activity_start_hour, test_activity_end_hour
)

print(f"\n📋 Meteomatics Format DataFrame:")
print(meteomatics_df)

# Show the weather data JSON structure
if not meteomatics_df.empty:
    weather_json = meteomatics_df['weather_data'].iloc[0]
    weather_data = json.loads(weather_json)
    
    print(f"\n🌤️ Weather Data JSON Structure:")
    print(f"   Total entries: {len(weather_data)}")
    print(f"   Sample entries:")
    for i, entry in enumerate(weather_data[:3]):
        print(f"     {i+1}. {entry}")
    if len(weather_data) > 3:
        print(f"     ... and {len(weather_data) - 3} more entries")
    
    print(f"\n🔍 Column Verification:")
    print(f"   ✅ latitude: {meteomatics_df['latitude'].iloc[0]}")
    print(f"   ✅ longitude: {meteomatics_df['longitude'].iloc[0]}")
    print(f"   ✅ meteomatics_run_hour_utc: {meteomatics_df['meteomatics_run_hour_utc'].iloc[0]}")
    print(f"   ✅ meteomatics_init_day: {meteomatics_df['meteomatics_init_day'].iloc[0]}")
    print(f"   ✅ meteomatics_init_month: {meteomatics_df['meteomatics_init_month'].iloc[0]}")
    print(f"   ✅ meteomatics_init_year: {meteomatics_df['meteomatics_init_year'].iloc[0]}")
    print(f"   ✅ data_source: {meteomatics_df['data_source'].iloc[0]}")
    print(f"   ✅ trip_day: {meteomatics_df['trip_day'].iloc[0]}")
    print(f"   ✅ last_modified: {meteomatics_df['last_modified'].iloc[0]}")
    
    print(f"\n💡 Usage Note:")
    print(f"   The updated filter_grib_data_by_location_and_time() function now returns:")
    print(f"   1. filtered_df (original format)")
    print(f"   2. output_csv (French CSV file path)")
    print(f"   3. meteomatics_df (new Meteomatics format)")

print(f"\n🎉 Meteomatics format structure test completed!")