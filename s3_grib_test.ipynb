# Import the S3 GRIB processor and required modules
from grib_processor_S3 import (
    list_s3_objects,
    get_s3_files_for_run_date,
    process_and_export_s3_grib_files,
    bucket_name
)
import pandas as pd
import os
from datetime import datetime

print("✅ Imports successful!")
print(f"📦 S3 Bucket: {bucket_name}")
print(f"🔧 Environment ready for testing")
print(f"\n💡 Next: Configure test parameters in the cell below")

print("🔄 Testing S3 connectivity...")

# List objects in S3 bucket
objects = list_s3_objects()

if objects:
    print(f"✅ S3 connectivity successful!")
    print(f"📊 Found {len(objects)} objects in bucket")
    print("\n📋 First 10 objects:")
    for i, obj in enumerate(objects[:10]):
        print(f"   {i+1:2d}. {obj}")
else:
    print("❌ No objects found in S3 bucket")

# Configure the test run date here
# Format: YYYY-MM-DD-HH (e.g., "2025-04-28-00")
test_run_date = "2025-06-17-06"  # Change this to test different dates

print(f"📅 Test configuration:")
print(f"   Run date: {test_run_date}")
print(f"   Enhanced features: Enabled")
print(f"   Coordinate rounding: 1 decimal place")
print(f"   Date filtering: Next 2 days only")
print(f"\n✅ Configuration set! Ready to run tests.")

# Test file discovery for the configured run date
if 'test_run_date' not in locals():
    print("⚠️ Please run the configuration cell above first!")
else:
    print(f"🔍 Searching for files with run date: {test_run_date}")
    
    matching_files = get_s3_files_for_run_date(test_run_date)
    
    if matching_files:
        print(f"✅ Found {len(matching_files)} matching files")
        print("\n📋 Files found:")
        for i, file_key in enumerate(matching_files[:5]):
            print(f"   {i+1}. {file_key}")
        if len(matching_files) > 5:
            print(f"   ... and {len(matching_files) - 5} more files")
    else:
        print(f"❌ No files found for run date: {test_run_date}")
        print("💡 Try a different run date in the configuration cell above")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True,
        upload_to_s3_bucket=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        print(f"📤 CSV exported to: {csv_file}")
        print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        
        # Show enhanced columns
        print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head())
        
        print(f"\n🎉 S3 GRIB processing successful!")
    else:
        print("❌ Processing failed")
else:
    print("⚠️ No files available for processing test")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True,
        upload_to_s3_bucket=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        print(f"📤 CSV exported to: {csv_file}")
        print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        
        # Show enhanced columns
        print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head())
        
        print(f"\n🎉 S3 GRIB processing successful!")
    else:
        print("❌ Processing failed")
else:
    print("⚠️ No files available for processing test")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True,
        upload_to_s3_bucket=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        print(f"📤 CSV exported to: {csv_file}")
        print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        
        # Show enhanced columns
        print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head())
        
        print(f"\n🎉 S3 GRIB processing successful!")
    else:
        print("❌ Processing failed")
else:
    print("⚠️ No files available for processing test")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True,
        upload_to_s3_bucket=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        print(f"📤 CSV exported to: {csv_file}")
        print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        
        # Show enhanced columns
        print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head())
        
        print(f"\n🎉 S3 GRIB processing successful!")
    else:
        print("❌ Processing failed")
else:
    print("⚠️ No files available for processing test")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True,
        upload_to_s3_bucket=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        print(f"📤 CSV exported to: {csv_file}")
        print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        
        # Show enhanced columns
        print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head())
        
        print(f"\n🎉 S3 GRIB processing successful!")
    else:
        print("❌ Processing failed")
else:
    print("⚠️ No files available for processing test")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features
    enhanced_df, csv_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        include_enhanced_columns=True,
        upload_to_s3_bucket=True
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    
    if not enhanced_df.empty and csv_file:
        print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
        print(f"📊 Data shape: {enhanced_df.shape}")
        print(f"📤 CSV exported to: {csv_file}")
        print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
        
        # Show enhanced columns
        print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
        available_cols = [col for col in sample_cols if col in enhanced_df.columns]
        print(enhanced_df[available_cols].head())
        
        print(f"\n🎉 S3 GRIB processing successful!")
    else:
        print("❌ Processing failed")
else:
    print("⚠️ No files available for processing test")

# Quick data analysis
if 'enhanced_df' in locals() and not enhanced_df.empty:
    print("📊 Quick Data Analysis")
    print("=" * 30)
    
    # Basic statistics
    print(f"Total rows: {len(enhanced_df):,}")
    print(f"Total files: {enhanced_df['file_name'].nunique()}")
    print(f"Geographic coverage (rounded to 1 decimal):")
    print(f"  Latitude: {enhanced_df['latitude'].min():.1f} to {enhanced_df['latitude'].max():.1f}")
    print(f"  Longitude: {enhanced_df['longitude'].min():.1f} to {enhanced_df['longitude'].max():.1f}")
    
    # Date filtering verification
    if 'forecasted_year' in enhanced_df.columns:
        unique_dates = enhanced_df[['forecasted_year', 'forecasted_month', 'forecasted_day']].drop_duplicates()
        print(f"\nForecast dates (filtered to next 2 days):")
        for _, row in unique_dates.iterrows():
            print(f"  {int(row['forecasted_year'])}-{int(row['forecasted_month']):02d}-{int(row['forecasted_day']):02d}")
    
    # Precipitation analysis
    if 'precipitation' in enhanced_df.columns:
        precip_stats = enhanced_df['precipitation'].describe()
        print(f"\nPrecipitation (mm):")
        print(f"  Min: {precip_stats['min']:.1f}")
        print(f"  Max: {precip_stats['max']:.1f}")
        print(f"  Mean: {precip_stats['mean']:.2f}")
        print(f"  Non-zero values: {(enhanced_df['precipitation'] > 0).sum():,}")
    
    # S3 metadata
    if 'min_date_upload' in enhanced_df.columns:
        print(f"\nS3 Upload metadata:")
        print(f"  Upload range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}")
    
    print(f"\n✅ Data analysis completed!")
    print(f"✅ Coordinates rounded to 1 decimal place")
    print(f"✅ Data filtered to next 2 days only")
else:
    print("⚠️ No data available for analysis")

# Test summary
print("📝 S3 GRIB Processor Test Summary")
print("=" * 40)

# Check test results
tests = {
    "S3 Connectivity": 'objects' in locals() and len(objects) > 0,
    "File Discovery": 'matching_files' in locals() and len(matching_files) > 0,
    "Data Processing": 'enhanced_df' in locals() and not enhanced_df.empty,
    "CSV Export": 'csv_file' in locals() and csv_file and os.path.exists(csv_file)
}

passed = sum(tests.values())
total = len(tests)

print(f"🎯 Results: {passed}/{total} tests passed")
print()

for test_name, result in tests.items():
    status = "✅ PASS" if result else "❌ FAIL"
    print(f"  {test_name:<20} {status}")

print()
if passed == total:
    print("🎉 ALL TESTS PASSED!")
    print("🚀 S3 GRIB processor is working perfectly!")
else:
    print("⚠️ Some tests failed. Check the output above for details.")

print("\n📚 Documentation:")
print("  • S3_Integration_Summary.md")
print("  • grib_processor_S3.py")