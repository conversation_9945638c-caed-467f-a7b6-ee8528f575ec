# Import the S3 GRIB processor and required modules
from grib_processor_S3 import (
    list_s3_objects,
    get_s3_files_for_run_date,
    process_and_export_s3_grib_files,
    bucket_name
)
import pandas as pd
import os
from datetime import datetime

print("✅ Imports successful!")
print(f"📦 S3 Bucket: {bucket_name}")
print(f"🔧 Environment ready for testing")
print(f"\n💡 Next: Configure test parameters in the cell below")

print("🔄 Testing S3 connectivity...")

# List objects in S3 bucket
objects = list_s3_objects()

if objects:
    print(f"✅ S3 connectivity successful!")
    print(f"📊 Found {len(objects)} objects in bucket")
    print("\n📋 First 10 objects:")
    for i, obj in enumerate(objects[:10]):
        print(f"   {i+1:2d}. {obj}")
else:
    print("❌ No objects found in S3 bucket")

# Configure the test run date here
# Format: YYYY-MM-DD-HH (e.g., "2025-04-28-00")
test_run_date = "2025-06-20-06"  # Change this to test different dates

print(f"📅 Test configuration:")
print(f"   Run date: {test_run_date}")
print(f"   Enhanced features: Enabled")
print(f"   Coordinate rounding: 1 decimal place")
print(f"   Date filtering: Next 2 days only")
print(f"\n✅ Configuration set! Ready to run tests.")

# Test file discovery for the configured run date
if 'test_run_date' not in locals():
    print("⚠️ Please run the configuration cell above first!")
else:
    print(f"🔍 Searching for files with run date: {test_run_date}")
    
    matching_files = get_s3_files_for_run_date(test_run_date)
    
    if matching_files:
        print(f"✅ Found {len(matching_files)} matching files")
        print("\n📋 Files found:")
        for i, file_key in enumerate(matching_files[:5]):
            print(f"   {i+1}. {file_key}")
        if len(matching_files) > 5:
            print(f"   ... and {len(matching_files) - 5} more files")
    else:
        print(f"❌ No files found for run date: {test_run_date}")
        print("💡 Try a different run date in the configuration cell above")

# Test the complete processing pipeline
if 'matching_files' in locals() and matching_files:
    print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
    
    start_time = datetime.now()
    
    # Process with enhanced features - test both CSV and Parquet formats
    print(f"\n🔄 Testing CSV format first...")
    # enhanced_df_csv, csv_file = process_and_export_s3_grib_files(
    #     run_date=test_run_date,
    #     include_enhanced_columns=True,
    #     upload_to_s3_bucket=True,
    #     output_format='csv'
    # )
    
    print(f"\n🔄 Testing Parquet format...")
    enhanced_df_parquet, parquet_file = process_and_export_s3_grib_files(
        run_date=test_run_date,
        upload_locally=True,
        output_format_locally='parquet',
        include_enhanced_columns=True,
        upload_to_s3_bucket=True,
        output_format='parquet'
    )
    
    processing_time = (datetime.now() - start_time).total_seconds()
    print(processing_time)
    
    
else:
    print("⚠️ No files available for processing test")

# Quick data analysis
if 'enhanced_df' in locals() and not enhanced_df.empty:
    print("📊 Quick Data Analysis")
    print("=" * 30)
    
    # Basic statistics
    print(f"Total rows: {len(enhanced_df):,}")
    print(f"Total files: {enhanced_df['file_name'].nunique()}")
    print(f"Geographic coverage (rounded to 1 decimal):")
    print(f"  Latitude: {enhanced_df['latitude'].min():.1f} to {enhanced_df['latitude'].max():.1f}")
    print(f"  Longitude: {enhanced_df['longitude'].min():.1f} to {enhanced_df['longitude'].max():.1f}")
    
    # Date filtering verification
    if 'forecasted_year' in enhanced_df.columns:
        unique_dates = enhanced_df[['forecasted_year', 'forecasted_month', 'forecasted_day']].drop_duplicates()
        print(f"\nForecast dates (filtered to next 2 days):")
        for _, row in unique_dates.iterrows():
            print(f"  {int(row['forecasted_year'])}-{int(row['forecasted_month']):02d}-{int(row['forecasted_day']):02d}")
    
    # Precipitation analysis
    if 'precipitation' in enhanced_df.columns:
        precip_stats = enhanced_df['precipitation'].describe()
        print(f"\nPrecipitation (mm):")
        print(f"  Min: {precip_stats['min']:.1f}")
        print(f"  Max: {precip_stats['max']:.1f}")
        print(f"  Mean: {precip_stats['mean']:.2f}")
        print(f"  Non-zero values: {(enhanced_df['precipitation'] > 0).sum():,}")
    
    # S3 metadata
    if 'min_date_upload' in enhanced_df.columns:
        print(f"\nS3 Upload metadata:")
        print(f"  Upload range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}")
    
    print(f"\n✅ Data analysis completed!")
    print(f"✅ Coordinates rounded to 1 decimal place")
    print(f"✅ Data filtered to next 2 days only")
else:
    print("⚠️ No data available for analysis")

# Test the location and time filtering function
from grib_processor_S3 import filter_grib_data_by_location_and_time

print("🎯 Testing Location and Time Filtering Function")
print("=" * 50)

# Test parameters
test_run_date = "2025-06-17-06"  # Must match a processed file in S3
test_latitude = 45.1             # Target latitude
test_longitude = 2.2             # Target longitude
test_activity_start_date = "2025-06-17"  # Activity start date
test_activity_end_date = "2025-06-17"    # Activity end date
test_activity_start_hour = 17             # Activity start hour (6 AM)
test_activity_end_hour = 17              # Activity end hour (6 PM)

print(f"📅 Run date: {test_run_date}")
print(f"📍 Target location: ({test_latitude}, {test_longitude})")
print(f"⏰ Activity period: {test_activity_start_date} {test_activity_start_hour:02d}:00 to {test_activity_end_date} {test_activity_end_hour:02d}:00")

try:
    # Call the filtering function
    filtered_df, output_csv = filter_grib_data_by_location_and_time(
        run_date=test_run_date,
        latitude=test_latitude,
        longitude=test_longitude,
        activity_start_date=test_activity_start_date,
        activity_end_date=test_activity_end_date,
        activity_start_hour=test_activity_start_hour,
        activity_end_hour=test_activity_end_hour,
        input_format='parquet'
    )
    
    if not filtered_df.empty and output_csv:
        print(f"\n✅ Filtering successful!")
        print(f"📊 Filtered data shape: {filtered_df.shape}")
        print(f"📁 Output file: {output_csv}")
        
        # Display sample of filtered data
        print(f"\n📋 Sample of filtered data:")
        print(filtered_df.head())
        
        # Show time range in filtered data
        if 'valid_time_fr' in filtered_df.columns:
            print(f"\n⏰ Time range in filtered data:")
            print(f"   First time: {filtered_df['valid_time_fr'].iloc[0]}")
            print(f"   Last time: {filtered_df['valid_time_fr'].iloc[-1]}")
        
        # Show precipitation statistics
        if 'precipitation' in filtered_df.columns:
            precip_stats = filtered_df['precipitation'].describe()
            print(f"\n🌧️ Precipitation statistics (mm):")
            print(f"   Min: {precip_stats['min']:.1f} mm")
            print(f"   Max: {precip_stats['max']:.1f} mm")
            print(f"   Mean: {precip_stats['mean']:.1f} mm")
            print(f"   Non-zero values: {(filtered_df['precipitation'] > 0).sum()}")
        
        # Verify file format
        if os.path.exists(output_csv):
            file_size = os.path.getsize(output_csv) / 1024  # KB
            print(f"\n📄 Output file details:")
            print(f"   File size: {file_size:.1f} KB")
            print(f"   Format: French CSV (semicolon separator, comma decimal)")
            
            # Show first few lines of CSV to verify format
            with open(output_csv, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()[:3]
            print(f"   Header: {lines[0].strip()}")
            if len(lines) > 1:
                print(f"   Sample: {lines[1].strip()}")
        
        print(f"\n🎉 Location and time filtering test PASSED!")
        
    else:
        print(f"❌ Filtering failed - no data returned")
        
except FileNotFoundError as e:
    print(f"❌ File not found: {e}")
    print(f"💡 Make sure you have run the main processing first to create the CSV file in S3")
    
except ValueError as e:
    print(f"❌ Data filtering error: {e}")
    print(f"💡 Try different coordinates or time period")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()

# Test summary
print("📝 S3 GRIB Processor Test Summary")
print("=" * 40)

# Check test results
tests = {
    "S3 Connectivity": 'objects' in locals() and len(objects) > 0,
    "File Discovery": 'matching_files' in locals() and len(matching_files) > 0,
    "Data Processing": 'enhanced_df' in locals() and not enhanced_df.empty,
    "CSV Export": 'csv_file' in locals() and csv_file and os.path.exists(csv_file),
    "Location Filtering": 'filtered_df' in locals() and not filtered_df.empty,
    "Time Filtering": 'output_csv' in locals() and output_csv and os.path.exists(output_csv)
}

passed = sum(tests.values())
total = len(tests)

print(f"🎯 Results: {passed}/{total} tests passed")
print()

for test_name, result in tests.items():
    status = "✅ PASS" if result else "❌ FAIL"
    print(f"  {test_name:<20} {status}")

print()
if passed == total:
    print("🎉 ALL TESTS PASSED!")
    print("🚀 S3 GRIB processor is working perfectly!")
else:
    print("⚠️ Some tests failed. Check the output above for details.")

print("\n📚 Documentation:")
print("  • S3_Integration_Summary.md")
print("  • grib_processor_S3.py")