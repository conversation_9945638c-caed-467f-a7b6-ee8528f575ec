{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# GRIB Processor Testing\n", "\n", "Test the GRIB processor functions step by step."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports successful!\n", "Archive folder exists: True\n"]}], "source": ["# Import functions\n", "from grib_processor import (\n", "    parse_ecmwf_filename,\n", "    get_files_for_run_date,\n", "    process_and_export_grib_files,\n", "    process_single_grib_file,\n", "    process_grib_files_for_run,\n", "    add_enhanced_columns\n", ")\n", "import pandas as pd\n", "import os\n", "\n", "print(\"Imports successful!\")\n", "print(f\"Archive folder exists: {os.path.exists('Archive')}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File: A1D04260000042602001\n", "  Run: 04/26 00:00\n", "  Forecast: 04/26 02:00\n", "\n", "File: A1S04261800042618011\n", "  Run: 04/26 18:00\n", "  Forecast: 04/26 18:01\n", "\n"]}], "source": ["# Test filename parsing\n", "test_files = [\"A1D04260000042602001\", \"A1S04261800042618011\"]\n", "\n", "for filename in test_files:\n", "    parsed = parse_ecmwf_filename(filename)\n", "    if parsed:\n", "        print(f\"File: {filename}\")\n", "        print(f\"  Run: {parsed['run_month']}/{parsed['run_day']} {parsed['run_hour']}:{parsed['run_minute']}\")\n", "        print(f\"  Forecast: {parsed['forecast_month']}/{parsed['forecast_day']} {parsed['forecast_hour']}:{parsed['forecast_minute']}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:Looking for files with run date: 042600\n", "INFO:grib_processor:Found 28 files for run date 2025-04-26-00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Found 28 files for 2025-04-26-00\n", "1. A1D04260000042601001\n", "2. A1D04260000042602001\n", "3. A1D04260000042603001\n", "4. A1D04260000042604001\n", "5. A1D04260000042605001\n"]}], "source": ["# Test file discovery\n", "run_date = \"2025-04-26-00\"\n", "files = get_files_for_run_date(run_date)\n", "print(f\"Found {len(files)} files for {run_date}\")\n", "for i, f in enumerate(files[:5]):\n", "    print(f\"{i+1}. {os.path.basename(f)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:Processing and exporting GRIB files for run date: 2025-04-28-00\n", "INFO:grib_processor:Processing GRIB files for run date: 2025-04-28-00\n", "INFO:grib_processor:Checking forecast file availability...\n", "INFO:grib_processor:Checking forecast file availability for run: 2025-04-28-00\n", "INFO:grib_processor:File availability check complete:\n", "INFO:grib_processor:  Total expected files: 24\n", "INFO:grib_processor:  Present files: 24\n", "INFO:grib_processor:  Missing files: 0\n", "INFO:grib_processor:  Availability rate: 100.0%\n", "INFO:grib_processor:✅ Data exported to: grib_availability_check_2025-04-28-00.csv\n", "INFO:grib_processor:📊 Exported 24 rows\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Availability check exported to: grib_availability_check_2025-04-28-00.csv\n", "Files present: 24/24\n", "        run_date             file_name forecasted_date  forecasted_hour  \\\n", "0  2025-04-28-00  A1D04280000042801001      2025-04-28                1   \n", "1  2025-04-28-00  A1D04280000042802001      2025-04-28                2   \n", "2  2025-04-28-00  A1D04280000042803001      2025-04-28                3   \n", "3  2025-04-28-00  A1D04280000042804001      2025-04-28                4   \n", "4  2025-04-28-00  A1D04280000042805001      2025-04-28                5   \n", "\n", "   is_present  \n", "0           1  \n", "1           1  \n", "2           1  \n", "3           1  \n", "4           1  \n"]}], "source": ["# Test availability check\n", "result, csv_file = process_and_export_grib_files(\n", "    \"2025-04-28-00\", \n", "    check_availability=True, \n", "    max_forecast_hours=24\n", ")\n", "\n", "print(f\"Availability check exported to: {csv_file}\")\n", "print(f\"Files present: {result['is_present'].sum()}/{len(result)}\")\n", "print(result.head())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:Processing and exporting GRIB files for run date: 2025-04-26-00\n", "INFO:grib_processor:Processing GRIB files for run date: 2025-04-26-00\n", "INFO:grib_processor:Looking for files with run date: 042600\n", "INFO:grib_processor:Found 28 files for run date 2025-04-26-00\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042601001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042601001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042601001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042602001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042602001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042602001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042603001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042603001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042603001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042604001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042604001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042604001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042605001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042605001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042605001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042606001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042606001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042606001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042607001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042607001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042607001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042608001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042608001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042608001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042609001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042609001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042609001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042610001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042610001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042610001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042611001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042611001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042611001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042612001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042612001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042612001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042613001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042613001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042613001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042614001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042614001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042614001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042615001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042615001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042615001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042616001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042616001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042616001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042617001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042617001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042617001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042618001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042618001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042618001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042619001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042619001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042619001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042621001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042621001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042621001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042622001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042622001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042622001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042623001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042623001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042623001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042700001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042700001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042700001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042900001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042900001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042900001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000043000001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000043000001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000043000001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000050100001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000050100001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000050100001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000050200001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000050200001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000050200001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000050300001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000050300001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000050300001\n", "INFO:grib_processor:All 28 files passed validation. Compiling data...\n", "INFO:grib_processor:✅ Processed A1D04260000042601001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042602001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042603001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042604001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042605001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042606001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042607001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042608001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042609001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042610001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042611001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042612001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042613001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042614001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042615001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042616001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042617001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042618001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042619001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042621001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042622001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042623001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042700001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000042900001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000043000001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000050100001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000050200001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04260000050300001: 16952 rows\n", "INFO:grib_processor:Adding upload date range columns...\n", "INFO:grib_processor:✅ Upload date range: 2025-04-29 15:26:00 to 2025-04-29 15:26:00\n", "INFO:grib_processor:✅ Successfully compiled data: 474656 total rows from 28 files\n", "INFO:grib_processor:✅ Data exported to: grib_compiled_data_2025-04-26-00_upload_2025-04-29-15-26.csv\n", "INFO:grib_processor:📊 Exported 474656 rows\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Data exported to: grib_compiled_data_2025-04-26-00_upload_2025-04-29-15-26.csv\n", "Data shape: (474656, 8)\n", "Columns: ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']\n", "   latitude  longitude       time          valid_time        tp  \\\n", "0      51.4       -5.9 2025-04-26 2025-04-26 01:00:00  0.000219   \n", "1      51.4       -5.8 2025-04-26 2025-04-26 01:00:00  0.000288   \n", "2      51.4       -5.7 2025-04-26 2025-04-26 01:00:00  0.000301   \n", "3      51.4       -5.6 2025-04-26 2025-04-26 01:00:00  0.000385   \n", "4      51.4       -5.5 2025-04-26 2025-04-26 01:00:00  0.000537   \n", "\n", "              file_name     min_date_upload     max_date_upload  \n", "0  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "1  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "2  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "3  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "4  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n"]}], "source": ["# Test data processing\n", "result, csv_file = process_and_export_grib_files(\"2025-04-26-00\")\n", "\n", "print(f\"Data exported to: {csv_file}\")\n", "print(f\"Data shape: {result.shape}\")\n", "print(f\"Columns: {list(result.columns)}\")\n", "if len(result) > 0:\n", "    print(result.head())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:Processing single GRIB file: Archive\\A1D04260000042601001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04260000042601001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04260000042601001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04260000042601001\n", "INFO:grib_processor:✅ Single file data exported to: single_file_check_A1D04260000042601001.csv\n", "INFO:grib_processor:📊 File contains 16952 rows and 11 columns\n", "INFO:grib_processor:📋 Data summary for A1D04260000042601001:\n", "INFO:grib_processor:   - Latitude range: 41.10 to 51.40\n", "INFO:grib_processor:   - Longitude range: -5.90 to 10.30\n", "INFO:grib_processor:   - TP (precipitation) range: 0.000000 to 0.008895\n", "INFO:grib_processor:   - Valid time: 2025-04-26 01:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File: A1D04260000042601001\n", "Variables check: PASSED\n", "Row check: PASSED\n", "CSV exported to: single_file_check_A1D04260000042601001.csv\n", "   latitude  longitude       time          valid_time        tp  \\\n", "0      51.4       -5.9 2025-04-26 2025-04-26 01:00:00  0.000219   \n", "1      51.4       -5.8 2025-04-26 2025-04-26 01:00:00  0.000288   \n", "2      51.4       -5.7 2025-04-26 2025-04-26 01:00:00  0.000301   \n", "3      51.4       -5.6 2025-04-26 2025-04-26 01:00:00  0.000385   \n", "4      51.4       -5.5 2025-04-26 2025-04-26 01:00:00  0.000537   \n", "\n", "              file_name     min_date_upload     max_date_upload  number  \\\n", "0  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00       0   \n", "1  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00       0   \n", "2  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00       0   \n", "3  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00       0   \n", "4  A1D04260000042601001 2025-04-29 15:26:00 2025-04-29 15:26:00       0   \n", "\n", "             step  surface  \n", "0 0 days 01:00:00      0.0  \n", "1 0 days 01:00:00      0.0  \n", "2 0 days 01:00:00      0.0  \n", "3 0 days 01:00:00      0.0  \n", "4 0 days 01:00:00      0.0  \n"]}], "source": ["# Test single file processing\n", "if os.path.exists('Archive'):\n", "    files = [f for f in os.listdir('Archive') if not f.endswith('.idx')]\n", "    if files:\n", "        test_file = os.path.join('Archive', files[0])\n", "        df, csv_file, validation = process_single_grib_file(test_file)\n", "        \n", "        print(f\"File: {validation['file_name']}\")\n", "        print(f\"Variables check: {'PASSED' if validation['variable_check_passed'] else 'FAILED'}\")\n", "        print(f\"Row check: {'PASSED' if validation['row_check_passed'] else 'FAILED'}\")\n", "        print(f\"CSV exported to: {csv_file}\")\n", "        if len(df) > 0:\n", "            print(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 NEW: Test Enhanced Columns Function\n", "\n", "**Enhanced GRIB Processor**: Test the new function that adds:\n", "- ✅ Decumulated precipitation (rounded to 1 decimal)\n", "- ✅ Paris timezone conversion (valid_time_fr)\n", "- ✅ Run date components (run_year, run_month, run_day, run_hour)\n", "- ✅ Forecast date components (forecasted_year, forecasted_month, forecasted_day, forecasted_hour)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:Processing GRIB files for run date: 2025-04-28-00\n", "INFO:grib_processor:Looking for files with run date: 042800\n", "INFO:grib_processor:Found 31 files for run date 2025-04-28-00\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042800011' est non compressé\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 Testing Enhanced GRIB Processor Function\n", "==================================================\n", "📅 Run date: 2025-04-28-00\n", "📊 Testing with 100000000000 rows\n", "\n", "🔄 Step 1: Processing basic GRIB data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042800011\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042800011\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042801001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042801001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042801001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042802001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042802001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042802001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042803001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042803001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042803001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042804001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042804001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042804001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042805001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042805001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042805001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042806001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042806001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042806001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042807001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042807001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042807001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042807001 (1)' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042807001 (1)\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042807001 (1)\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042808001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042808001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042808001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042809001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042809001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042809001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042810001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042810001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042810001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042811001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042811001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042811001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042812001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042812001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042812001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042813001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042813001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042813001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042814001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042814001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042814001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042815001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042815001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042815001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042816001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042816001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042816001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042817001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042817001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042817001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042818001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042818001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042818001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042819001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042819001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042819001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042820001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042820001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042820001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042821001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042821001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042821001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042822001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042822001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042822001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042823001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042823001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042823001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000042900001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000042900001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000042900001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000050100001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000050100001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000050100001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000050200001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000050200001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000050200001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000050300001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000050300001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000050300001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000050400001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000050400001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000050400001\n", "INFO:grib_processor:📂 Le fichier 'Archive\\A1D04280000050500001' est non compressé\n", "INFO:grib_processor:✅ Fichier GRIB chargé avec succès\n", "INFO:grib_processor:✅ All required variables found in A1D04280000050500001\n", "INFO:grib_processor:✅ Correct row count (16952) in A1D04280000050500001\n", "INFO:grib_processor:All 31 files passed validation. Compiling data...\n", "INFO:grib_processor:✅ Processed A1D04280000042800011: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042801001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042802001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042803001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042804001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042805001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042806001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042807001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042807001 (1): 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042808001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042809001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042810001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042811001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042812001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042813001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042814001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042815001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042816001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042817001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042818001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042819001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042820001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042821001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042822001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042823001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000042900001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000050100001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000050200001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000050300001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000050400001: 16952 rows\n", "INFO:grib_processor:✅ Processed A1D04280000050500001: 16952 rows\n", "INFO:grib_processor:Adding upload date range columns...\n", "INFO:grib_processor:✅ Upload date range: 2025-04-29 15:26:00 to 2025-04-29 15:26:00\n", "INFO:grib_processor:✅ Successfully compiled data: 525512 total rows from 31 files\n", "INFO:grib_processor:Adding enhanced columns to DataFrame...\n", "INFO:grib_processor:Decumulating precipitation data...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["test longueur\n", "16952\n", "        latitude  longitude       time valid_time        tp  \\\n", "0           51.4       -5.9 2025-04-28 2025-04-28  0.000000   \n", "1           51.4       -5.8 2025-04-28 2025-04-28  0.000000   \n", "2           51.4       -5.7 2025-04-28 2025-04-28  0.000000   \n", "3           51.4       -5.6 2025-04-28 2025-04-28  0.000000   \n", "4           51.4       -5.5 2025-04-28 2025-04-28  0.000000   \n", "...          ...        ...        ...        ...       ...   \n", "525507      41.1        9.9 2025-04-28 2025-05-05  0.026358   \n", "525508      41.1       10.0 2025-04-28 2025-05-05  0.013628   \n", "525509      41.1       10.1 2025-04-28 2025-05-05  0.006432   \n", "525510      41.1       10.2 2025-04-28 2025-05-05  0.004841   \n", "525511      41.1       10.3 2025-04-28 2025-05-05  0.005991   \n", "\n", "                   file_name     min_date_upload     max_date_upload  \n", "0       A1D04280000042800011 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "1       A1D04280000042800011 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "2       A1D04280000042800011 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "3       A1D04280000042800011 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "4       A1D04280000042800011 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "...                      ...                 ...                 ...  \n", "525507  A1D04280000050500001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "525508  A1D04280000050500001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "525509  A1D04280000050500001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "525510  A1D04280000050500001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "525511  A1D04280000050500001 2025-04-29 15:26:00 2025-04-29 15:26:00  \n", "\n", "[525512 rows x 8 columns]\n", "✅ Basic data loaded: 525,512 rows\n", "📋 Basic columns: ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']\n", "\n", "🔄 Step 2: Testing enhanced function...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:grib_processor:✅ Precipitation decumulation completed\n", "INFO:grib_processor:Converting timezone from UTC to Paris...\n", "INFO:grib_processor:✅ Timezone conversion completed\n", "INFO:grib_processor:Extracting run date components...\n", "INFO:grib_processor:✅ Run date components extracted\n", "INFO:grib_processor:Extracting forecast date components...\n", "INFO:grib_processor:✅ Forecast date components extracted\n", "INFO:grib_processor:✅ Enhanced DataFrame created with 525512 rows and 18 columns\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Enhanced processing completed!\n", "📊 Enhanced data: (525512, 18)\n", "📋 Enhanced columns: ['latitude', 'longitude', 'time', 'valid_time', 'valid_time_fr', 'tp', 'precipitation', 'file_name', 'min_date_upload', 'max_date_upload', 'run_year', 'run_month', 'run_day', 'run_hour', 'forecasted_year', 'forecasted_month', 'forecasted_day', 'forecasted_hour']\n", "\n", "📋 Sample enhanced data:\n", "       latitude  longitude             valid_time_fr   tp  precipitation  \\\n", "16789      41.1       -5.9 2025-04-28 02:00:00+02:00  0.0            0.0   \n", "33741      41.1       -5.9 2025-04-28 03:00:00+02:00  0.0            0.0   \n", "50693      41.1       -5.9 2025-04-28 04:00:00+02:00  0.0            0.0   \n", "67645      41.1       -5.9 2025-04-28 05:00:00+02:00  0.0            0.0   \n", "84597      41.1       -5.9 2025-04-28 06:00:00+02:00  0.0            0.0   \n", "\n", "       run_month  run_day  forecasted_hour  \n", "16789          4       28                2  \n", "33741          4       28                3  \n", "50693          4       28                4  \n", "67645          4       28                5  \n", "84597          4       28                6  \n", "\n", "🔍 Quick Verification:\n", "   📊 Precipitation: min=-0.0, max=60.6 mm\n", "   🌍 Timezone: 2025-04-28 00:00:00+00:00 → 2025-04-28 02:00:00+02:00\n", "   📅 Run date: 2025-04-28\n", "   📅 Forecast date: 2025-04-28\n", "\n", "📤 Step 5: Testing CSV export...\n", "✅ Test data exported to: enhanced_test_data_2025-04-28-00.csv\n", "📊 File size: 89,509,807 bytes\n", "\n", "🎉 Enhanced function test completed successfully!\n"]}], "source": ["# 🧪 ENHANCED FUNCTION TEST\n", "print(\"🚀 Testing Enhanced GRIB Processor Function\")\n", "print(\"=\" * 50)\n", "\n", "# Test parameters\n", "run_date = \"2025-04-28-00\"\n", "test_rows = 100000000000  # Small subset for quick testing\n", "\n", "print(f\"📅 Run date: {run_date}\")\n", "print(f\"📊 Testing with {test_rows} rows\")\n", "\n", "try:\n", "    # Step 1: Get basic data\n", "    print(\"\\n🔄 Step 1: Processing basic GRIB data...\")\n", "    basic_df = process_grib_files_for_run(run_date)\n", "    print(\"test longueur\")\n", "    print(len(df))\n", "    print(basic_df)\n", "    \n", "    if basic_df.empty or 'variable_check' in basic_df.columns:\n", "        print(\"❌ No valid basic data available\")\n", "    else:\n", "        print(f\"✅ Basic data loaded: {basic_df.shape[0]:,} rows\")\n", "        print(f\"📋 Basic columns: {list(basic_df.columns)}\")\n", "        \n", "        # Step 2: Test enhanced function\n", "        print(f\"\\n🔄 Step 2: Testing enhanced function...\")\n", "        # test_subset = basic_df.head(test_rows)\n", "        test_subset = basic_df\n", "        enhanced_df = add_enhanced_columns(test_subset)\n", "        \n", "        print(f\"✅ Enhanced processing completed!\")\n", "        print(f\"📊 Enhanced data: {enhanced_df.shape}\")\n", "        print(f\"📋 Enhanced columns: {list(enhanced_df.columns)}\")\n", "        \n", "        # Step 3: Show sample data\n", "        print(\"\\n📋 Sample enhanced data:\")\n", "        display_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation', \n", "                       'run_month', 'run_day', 'forecasted_hour']\n", "        print(enhanced_df[display_cols].head())\n", "        \n", "        # Step 4: Quick verification\n", "        print(\"\\n🔍 Quick Verification:\")\n", "        \n", "        # Check precipitation stats\n", "        precip_stats = enhanced_df['precipitation'].describe()\n", "        print(f\"   📊 Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm\")\n", "        \n", "        # Check timezone conversion\n", "        sample_utc = enhanced_df['valid_time'].iloc[0]\n", "        sample_paris = enhanced_df['valid_time_fr'].iloc[0]\n", "        print(f\"   🌍 Timezone: {sample_utc} → {sample_paris}\")\n", "        \n", "        # Check date components\n", "        sample_row = enhanced_df.iloc[0]\n", "        print(f\"   📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}\")\n", "        print(f\"   📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}\")\n", "        \n", "        # round latitude and longitude\n", "        enhanced_df['latitude'] = enhanced_df['latitude'].round(1)\n", "        enhanced_df['longitude'] = enhanced_df['longitude'].round(1)\n", "\n", "\n", "        # Step 5: Export test\n", "        print(\"\\n📤 Step 5: Testing CSV export...\")\n", "        output_file = f\"enhanced_test_data_{run_date.replace(':', '-')}.csv\"\n", "        enhanced_df.to_csv(output_file, index=False)\n", "        \n", "        print(f\"✅ Test data exported to: {output_file}\")\n", "        print(f\"📊 File size: {os.path.getsize(output_file):,} bytes\")\n", "        \n", "        print(\"\\n🎉 Enhanced function test completed successfully!\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error during testing: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import boto3\n", "import cfgrib\n", "import tempfile\n", "import xarray as xr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arn:aws:secretsmanager:eu-west-3:211125471602:secret:GOOGLE_GEOCODING_API_KEY-CG7HdE"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<xarray.Dataset>\n", "Dimensions:     (latitude: 104, longitude: 163)\n", "Coordinates:\n", "    number      int32 ...\n", "    time        datetime64[ns] ...\n", "    step        timedelta64[ns] ...\n", "    surface     float64 ...\n", "  * latitude    (latitude) float64 51.4 51.3 51.2 51.1 ... 41.4 41.3 41.2 41.1\n", "  * longitude   (longitude) float64 -5.9 -5.8 -5.7 -5.6 ... 10.0 10.1 10.2 10.3\n", "    valid_time  datetime64[ns] ...\n", "Data variables:\n", "    tp          (latitude, longitude) float32 ...\n", "Attributes:\n", "    GRIB_edition:            1\n", "    GRIB_centre:             ecmf\n", "    GRIB_centreDescription:  European Centre for Medium-Range Weather Forecasts\n", "    GRIB_subCentre:          0\n", "    Conventions:             CF-1.7\n", "    institution:             European Centre for Medium-Range Weather Forecasts\n", "    history:                 2025-06-16T18:35 GRIB to CDM+CF via cfgrib-0.9.1...\n"]}], "source": ["import boto3\n", "import xarray as xr\n", "import tempfile\n", "import os\n", "\n", "# Enter your Access keys\n", "aws_access_key_id = \"********************\"\n", "aws_secret_access_key = \"MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf\"\n", "region_name = 'eu-west-3'  # Exemple : 'us-east-1', 'eu-west-3', etc.\n", "\n", "# Paramètres du bucket\n", "bucket_name = 'ecmwf-forecast-data-source'\n", "object_key = 'A1D04260000042606001'\n", "\n", "# Connexion à S3\n", "s3 = boto3.client(\n", "    's3',\n", "    aws_access_key_id=aws_access_key_id,\n", "    aws_secret_access_key=aws_secret_access_key,\n", "    region_name=region_name\n", ")\n", "\n", "# <PERSON><PERSON><PERSON> un fichier temporaire, mais sans le garder ouvert\n", "tmp_file = tempfile.NamedTemporaryFile(suffix=\".grib\", delete=False)\n", "tmp_path = tmp_file.name\n", "tmp_file.close()  # Fermer immédiatement pour que boto3 puisse y écrire\n", "\n", "# Télécharger le fichier S3 dans le fichier temporaire\n", "s3.download_file(bucket_name, object_key, tmp_path)\n", "\n", "# <PERSON><PERSON> le fichier GRIB avec cfgrib\n", "try:\n", "    ds = xr.open_dataset(tmp_path, engine='cfgrib')\n", "    print(ds)\n", "finally:\n", "    os.remove(tmp_path)  # Supprimer le fichier temporaire proprement\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}