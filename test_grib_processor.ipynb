# Import functions
from grib_processor import (
    parse_ecmwf_filename,
    get_files_for_run_date,
    process_and_export_grib_files,
    process_single_grib_file,
    process_grib_files_for_run,
    add_enhanced_columns
)
import pandas as pd
import os

print("Imports successful!")
print(f"Archive folder exists: {os.path.exists('Archive')}")

# Test filename parsing
test_files = ["A1D04260000042602001", "A1S04261800042618011"]

for filename in test_files:
    parsed = parse_ecmwf_filename(filename)
    if parsed:
        print(f"File: {filename}")
        print(f"  Run: {parsed['run_month']}/{parsed['run_day']} {parsed['run_hour']}:{parsed['run_minute']}")
        print(f"  Forecast: {parsed['forecast_month']}/{parsed['forecast_day']} {parsed['forecast_hour']}:{parsed['forecast_minute']}")
    print()

# Test file discovery
run_date = "2025-04-26-00"
files = get_files_for_run_date(run_date)
print(f"Found {len(files)} files for {run_date}")
for i, f in enumerate(files[:5]):
    print(f"{i+1}. {os.path.basename(f)}")

# Test availability check
result, csv_file = process_and_export_grib_files(
    "2025-04-28-00", 
    check_availability=True, 
    max_forecast_hours=24
)

print(f"Availability check exported to: {csv_file}")
print(f"Files present: {result['is_present'].sum()}/{len(result)}")
print(result.head())

# Test data processing
result, csv_file = process_and_export_grib_files("2025-04-26-00")

print(f"Data exported to: {csv_file}")
print(f"Data shape: {result.shape}")
print(f"Columns: {list(result.columns)}")
if len(result) > 0:
    print(result.head())

# Test single file processing
if os.path.exists('Archive'):
    files = [f for f in os.listdir('Archive') if not f.endswith('.idx')]
    if files:
        test_file = os.path.join('Archive', files[0])
        df, csv_file, validation = process_single_grib_file(test_file)
        
        print(f"File: {validation['file_name']}")
        print(f"Variables check: {'PASSED' if validation['variable_check_passed'] else 'FAILED'}")
        print(f"Row check: {'PASSED' if validation['row_check_passed'] else 'FAILED'}")
        print(f"CSV exported to: {csv_file}")
        if len(df) > 0:
            print(df.head())

# 🧪 ENHANCED FUNCTION TEST
print("🚀 Testing Enhanced GRIB Processor Function")
print("=" * 50)

# Test parameters
run_date = "2025-04-28-00"
test_rows = 100000000000  # Small subset for quick testing

print(f"📅 Run date: {run_date}")
print(f"📊 Testing with {test_rows} rows")

try:
    # Step 1: Get basic data
    print("\n🔄 Step 1: Processing basic GRIB data...")
    basic_df = process_grib_files_for_run(run_date)
    print("test longueur")
    print(len(df))
    print(basic_df)
    
    if basic_df.empty or 'variable_check' in basic_df.columns:
        print("❌ No valid basic data available")
    else:
        print(f"✅ Basic data loaded: {basic_df.shape[0]:,} rows")
        print(f"📋 Basic columns: {list(basic_df.columns)}")
        
        # Step 2: Test enhanced function
        print(f"\n🔄 Step 2: Testing enhanced function...")
        # test_subset = basic_df.head(test_rows)
        test_subset = basic_df
        enhanced_df = add_enhanced_columns(test_subset)
        
        print(f"✅ Enhanced processing completed!")
        print(f"📊 Enhanced data: {enhanced_df.shape}")
        print(f"📋 Enhanced columns: {list(enhanced_df.columns)}")
        
        # Step 3: Show sample data
        print("\n📋 Sample enhanced data:")
        display_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation', 
                       'run_month', 'run_day', 'forecasted_hour']
        print(enhanced_df[display_cols].head())
        
        # Step 4: Quick verification
        print("\n🔍 Quick Verification:")
        
        # Check precipitation stats
        precip_stats = enhanced_df['precipitation'].describe()
        print(f"   📊 Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm")
        
        # Check timezone conversion
        sample_utc = enhanced_df['valid_time'].iloc[0]
        sample_paris = enhanced_df['valid_time_fr'].iloc[0]
        print(f"   🌍 Timezone: {sample_utc} → {sample_paris}")
        
        # Check date components
        sample_row = enhanced_df.iloc[0]
        print(f"   📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}")
        print(f"   📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}")
        
        # round latitude and longitude
        enhanced_df['latitude'] = enhanced_df['latitude'].round(1)
        enhanced_df['longitude'] = enhanced_df['longitude'].round(1)


        # Step 5: Export test
        print("\n📤 Step 5: Testing CSV export...")
        output_file = f"enhanced_test_data_{run_date.replace(':', '-')}.csv"
        enhanced_df.to_csv(output_file, index=False)
        
        print(f"✅ Test data exported to: {output_file}")
        print(f"📊 File size: {os.path.getsize(output_file):,} bytes")
        
        print("\n🎉 Enhanced function test completed successfully!")
        
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()

import boto3
import cfgrib
import tempfile
import xarray as xr

arn:aws:secretsmanager:eu-west-3:211125471602:secret:GOOGLE_GEOCODING_API_KEY-CG7HdE

import boto3
import xarray as xr
import tempfile
import os

# Enter your Access keys
aws_access_key_id = "********************"
aws_secret_access_key = "MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf"
region_name = 'eu-west-3'  # Exemple : 'us-east-1', 'eu-west-3', etc.

# Paramètres du bucket
bucket_name = 'ecmwf-forecast-data-source'
object_key = 'A1D04260000042606001'

# Connexion à S3
s3 = boto3.client(
    's3',
    aws_access_key_id=aws_access_key_id,
    aws_secret_access_key=aws_secret_access_key,
    region_name=region_name
)

# Créer un fichier temporaire, mais sans le garder ouvert
tmp_file = tempfile.NamedTemporaryFile(suffix=".grib", delete=False)
tmp_path = tmp_file.name
tmp_file.close()  # Fermer immédiatement pour que boto3 puisse y écrire

# Télécharger le fichier S3 dans le fichier temporaire
s3.download_file(bucket_name, object_key, tmp_path)

# Lire le fichier GRIB avec cfgrib
try:
    ds = xr.open_dataset(tmp_path, engine='cfgrib')
    print(ds)
finally:
    os.remove(tmp_path)  # Supprimer le fichier temporaire proprement
