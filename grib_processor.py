import os
import bz2
import pandas as pd
import xarray as xr
from datetime import datetime
import glob
import logging
import boto3
from botocore.exceptions import ClientError

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# S3 Configuration
S3_UPLOAD_BUCKET = "ecmwf-forecast-by-run-date"
S3_REGION = "eu-west-3"

def export_to_french_csv(df, filename):
    """
    Export DataFrame to French CSV format:
    - Decimal separator: comma (,)
    - Column separator: semicolon (;)
    - Date format: DD/MM/YYYY
    - Encoding: UTF-8 with BOM
    """
    logger.info(f"Exporting to French CSV format: {filename}")

    # Create a copy to avoid modifying the original DataFrame
    df_french = df.copy()

    # Convert date columns to French format (DD/MM/YYYY)
    date_columns = ['time', 'valid_time', 'valid_time_fr', 'min_date_upload', 'max_date_upload']

    for col in date_columns:
        if col in df_french.columns:
            # Handle different date formats
            if df_french[col].dtype == 'datetime64[ns]' or pd.api.types.is_datetime64_any_dtype(df_french[col]):
                df_french[col] = pd.to_datetime(df_french[col]).dt.strftime('%d/%m/%Y %H:%M')
            elif df_french[col].dtype == 'object':
                # Try to convert string dates
                try:
                    df_french[col] = pd.to_datetime(df_french[col]).dt.strftime('%d/%m/%Y %H:%M')
                except:
                    pass  # Keep original format if conversion fails

    # Convert numeric columns to use comma as decimal separator
    numeric_columns = df_french.select_dtypes(include=['float64', 'float32', 'int64', 'int32']).columns

    for col in numeric_columns:
        # Convert to string with comma as decimal separator
        df_french[col] = df_french[col].astype(str).str.replace('.', ',')

    # Export with French CSV format
    df_french.to_csv(
        filename,
        sep=';',           # Column separator
        decimal=',',       # Decimal separator (though we already converted)
        index=False,       # No row indices
        encoding='utf-8-sig'  # UTF-8 with BOM
    )

    logger.info(f"✅ French CSV exported: {filename}")
    return filename

def upload_to_s3(local_file_path, s3_key, bucket_name=S3_UPLOAD_BUCKET):
    """
    Upload a file to S3 bucket.

    Args:
        local_file_path (str): Path to local file
        s3_key (str): S3 object key (filename in bucket)
        bucket_name (str): S3 bucket name

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Uploading {local_file_path} to S3 bucket {bucket_name}")

        # Create S3 client
        s3_client = boto3.client('s3', region_name=S3_REGION)

        # Upload file
        s3_client.upload_file(local_file_path, bucket_name, s3_key)

        logger.info(f"✅ Successfully uploaded to S3: s3://{bucket_name}/{s3_key}")
        return True

    except ClientError as e:
        logger.error(f"❌ S3 upload failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error during S3 upload: {e}")
        return False

def est_bz2(filepath):
    """Détecte si un fichier est un bzip2 grâce à sa signature magique."""
    with open(filepath, 'rb') as f:
        signature = f.read(3)
    return signature == b'BZh'

def lire_fichier_ecmwf_auto(filepath):
    """
    Charge un fichier GRIB (compressé ou non) et retourne le dataset xarray.
    """
    # Vérifie si le fichier est compressé
    is_compressed = est_bz2(filepath)

    if is_compressed:
        logger.info(f"📦 Le fichier '{filepath}' est compressé (bzip2)")
        with bz2.open(filepath, 'rb') as f:
            contenu = f.read()

        temp_grib = 'temp_decompressed.grib'
        with open(temp_grib, 'wb') as temp:
            temp.write(contenu)
        chemin_grib = temp_grib
    else:
        logger.info(f"📂 Le fichier '{filepath}' est non compressé")
        chemin_grib = filepath

    try:
        # Chargement du fichier GRIB avec xarray
        ds = xr.open_dataset(chemin_grib, engine='cfgrib')
        logger.info("✅ Fichier GRIB chargé avec succès")
        return ds, is_compressed
    except Exception as e:
        logger.error(f"❌ Erreur de lecture GRIB : {e}")
        return None, is_compressed
    finally:
        if is_compressed and os.path.exists(temp_grib):
            os.remove(temp_grib)

def parse_ecmwf_filename(filename):
    """
    Parse ECMWF filename according to convention: ccSMMDDHHIImmddhhiiE

    Args:
        filename (str): ECMWF filename

    Returns:
        dict: Parsed components or None if invalid format
    """
    if len(filename) < 19:
        return None

    try:
        parsed = {
            'dissemination_stream': filename[:2],  # cc
            'stream_indicator': filename[2],       # S
            'run_month': filename[3:5],           # MM
            'run_day': filename[5:7],             # DD
            'run_hour': filename[7:9],            # HH
            'run_minute': filename[9:11],         # II
            'forecast_month': filename[11:13],    # mm
            'forecast_day': filename[13:15],      # dd
            'forecast_hour': filename[15:17],     # hh
            'forecast_minute': filename[17:19],   # ii
            'experiment': filename[19:] if len(filename) > 19 else ''  # E
        }

        # Validate that all components are numeric where expected
        numeric_fields = ['run_month', 'run_day', 'run_hour', 'run_minute',
                         'forecast_month', 'forecast_day', 'forecast_hour', 'forecast_minute']

        for field in numeric_fields:
            if not parsed[field].isdigit():
                return None

        return parsed
    except (IndexError, ValueError):
        return None

def get_files_for_run_date(run_date, archive_folder="Archive"):
    """
    Trouve tous les fichiers GRIB correspondant à une date de run donnée selon les conventions ECMWF.

    Args:
        run_date (str): Date au format "YYYY-MM-DD-HH"
        archive_folder (str): Dossier contenant les fichiers GRIB

    Returns:
        list: Liste des chemins vers les fichiers correspondants
    """
    try:
        date_obj = datetime.strptime(run_date, "%Y-%m-%d-%H")
        run_hour = f"{date_obj.hour:02d}"

        # Extract month and day for pattern matching
        run_month = f"{date_obj.month:02d}"
        run_day = f"{date_obj.day:02d}"

        logger.info(f"Looking for files with run date: {run_month}{run_day}{run_hour}")

        # Find all files in archive folder
        all_files = []
        for file in os.listdir(archive_folder):
            # Skip index files and other non-GRIB files
            if file.endswith('.idx') or file.startswith('.'):
                continue

            # Parse the filename according to ECMWF convention
            parsed = parse_ecmwf_filename(file)
            if parsed is None:
                continue

            # Check if this file matches our run date
            if (parsed['run_month'] == run_month and
                parsed['run_day'] == run_day and
                parsed['run_hour'] == run_hour):
                full_path = os.path.join(archive_folder, file)
                all_files.append(full_path)

        logger.info(f"Found {len(all_files)} files for run date {run_date}")
        return sorted(all_files)

    except ValueError as e:
        logger.error(f"Invalid date format. Expected YYYY-MM-DD or YYYY-MM-DD-HH, got: {run_date}")
        raise ValueError(f"Invalid date format. Expected YYYY-MM-DD or YYYY-MM-DD-HH, got: {run_date}")

def generate_expected_filename(run_datetime, forecast_datetime, dissemination_stream="A1", stream_indicator="D", experiment="001"):
    """
    Generate expected ECMWF filename based on run and forecast datetimes.
    Modified to match actual file format: ccSMMDDHHIImmddhhE (without forecast minutes)

    Args:
        run_datetime (datetime): Run datetime
        forecast_datetime (datetime): Forecast datetime
        dissemination_stream (str): cc part (default: "A1")
        stream_indicator (str): S part (default: "D")
        experiment (str): E part (default: "001")

    Returns:
        str: Expected filename
    """
    filename = (
        f"{dissemination_stream}"
        f"{stream_indicator}"
        f"{run_datetime.month:02d}"
        f"{run_datetime.day:02d}"
        f"{run_datetime.hour:02d}"
        f"{run_datetime.minute:02d}"
        f"{forecast_datetime.month:02d}"
        f"{forecast_datetime.day:02d}"
        f"{forecast_datetime.hour:02d}"
        f"{experiment}"
    )
    return filename

def find_matching_files(expected_pattern, available_files):
    """
    Find files that match the expected pattern, handling different experiment versions.

    Args:
        expected_pattern (str): Expected filename pattern
        available_files (set): Set of available filenames

    Returns:
        str or None: Matching filename or None if not found
    """
    # Try exact match first
    if expected_pattern in available_files:
        return expected_pattern

    # Try different experiment versions
    base_pattern = expected_pattern[:-3]  # Remove last 3 characters (experiment part)

    # Common experiment patterns
    experiment_patterns = ["001", "1", "11", "0001"]

    for exp in experiment_patterns:
        candidate = base_pattern + exp
        if candidate in available_files:
            return candidate

    return None

def check_forecast_files_availability(run_date, archive_folder="Archive", max_forecast_hours=72):
    """
    Check if all expected forecast files are present for a given run date.

    Args:
        run_date (str): Run date in format "YYYY-MM-DD-HH"
        archive_folder (str): Directory containing GRIB files
        max_forecast_hours (int): Maximum forecast hours to check (default: 72)

    Returns:
        pandas.DataFrame: DataFrame with file availability information
    """
    try:
        # Parse the run date
        run_datetime = datetime.strptime(run_date, "%Y-%m-%d-%H")

        logger.info(f"Checking forecast file availability for run: {run_date}")

        # Get list of all files in archive
        available_files = set()
        for file in os.listdir(archive_folder):
            if not file.endswith('.idx') and not file.startswith('.'):
                available_files.add(file)

        # Check each forecast hour from 1 to max_forecast_hours
        results = []

        for forecast_step in range(1, max_forecast_hours + 1):
            # Calculate forecast datetime
            forecast_datetime = run_datetime + pd.Timedelta(hours=forecast_step)

            # Generate expected filename patterns (try different stream combinations)
            expected_patterns = []

            # Common ECMWF stream patterns
            stream_combinations = [
                ("A1", "D"),  # Deterministic
                ("A1", "S"),  # Ensemble/Statistical
            ]

            for dissem_stream, stream_ind in stream_combinations:
                expected_filename = generate_expected_filename(
                    run_datetime, forecast_datetime,
                    dissem_stream, stream_ind
                )
                expected_patterns.append(expected_filename)

            # Check if any of the expected patterns exist using flexible matching
            file_found = False
            found_filename = None

            for pattern in expected_patterns:
                matched_file = find_matching_files(pattern, available_files)
                if matched_file:
                    file_found = True
                    found_filename = matched_file
                    break

            # Add result
            results.append({
                'run_date': run_date,
                'file_name': found_filename if file_found else expected_patterns[0],
                'forecasted_date': forecast_datetime.strftime("%Y-%m-%d"),
                'forecasted_hour': forecast_datetime.hour,
                'is_present': 1 if file_found else 0
            })

        # Create DataFrame
        df_results = pd.DataFrame(results)

        # Summary statistics
        total_files = len(df_results)
        present_files = df_results['is_present'].sum()
        missing_files = total_files - present_files

        logger.info(f"File availability check complete:")
        logger.info(f"  Total expected files: {total_files}")
        logger.info(f"  Present files: {present_files}")
        logger.info(f"  Missing files: {missing_files}")
        logger.info(f"  Availability rate: {(present_files/total_files)*100:.1f}%")

        return df_results

    except ValueError as e:
        logger.error(f"Invalid date format. Expected YYYY-MM-DD-HH, got: {run_date}")
        raise ValueError(f"Invalid date format. Expected YYYY-MM-DD-HH, got: {run_date}")
    except Exception as e:
        logger.error(f"Error checking file availability: {e}")
        return pd.DataFrame()

def validate_grib_file(filepath):
    """
    Valide un fichier GRIB selon les critères spécifiés.

    Args:
        filepath (str): Chemin vers le fichier GRIB

    Returns:
        dict: Dictionnaire avec les résultats de validation
    """
    file_name = os.path.basename(filepath)
    result = {
        'file_name': file_name,
        'variable_check': 0,
        'row_check': 0,
        'dataset': None
    }

    try:
        # Charger le fichier
        ds, is_compressed = lire_fichier_ecmwf_auto(filepath)

        if ds is None:
            logger.warning(f"Could not load file: {file_name}")
            return result

        # Vérifier les variables requises
        required_vars = ['latitude', 'longitude', 'valid_time', 'tp']

        # Check if variables exist in the dataset
        available_vars = list(ds.variables.keys())
        available_coords = list(ds.coords.keys())
        all_available = available_vars + available_coords

        missing_vars = []
        for var in required_vars:
            if var not in all_available:
                missing_vars.append(var)

        if not missing_vars:
            result['variable_check'] = 1
            logger.info(f"✅ All required variables found in {file_name}")
        else:
            logger.warning(f"❌ Missing variables in {file_name}: {missing_vars}")

        # Vérifier le nombre de lignes (16,952)
        # Convert to DataFrame to count rows
        df = ds.to_dataframe()
        row_count = len(df)

        if row_count == 16952:
            result['row_check'] = 1
            logger.info(f"✅ Correct row count ({row_count}) in {file_name}")
        else:
            logger.warning(f"❌ Incorrect row count in {file_name}: {row_count} (expected 16952)")

        # Store dataset for later use if validation passes
        if result['variable_check'] == 1 and result['row_check'] == 1:
            result['dataset'] = ds

        return result

    except Exception as e:
        logger.error(f"Error validating file {file_name}: {e}")
        return result

def process_grib_files_for_run(run_date, archive_folder="Archive", check_availability=False, max_forecast_hours=72):
    """
    Fonction principale qui traite tous les fichiers GRIB pour une date de run donnée.

    Args:
        run_date (str): Date de run au format "YYYY-MM-DD" ou "YYYY-MM-DD-HH"
        archive_folder (str): Dossier contenant les fichiers GRIB
        check_availability (bool): Si True, vérifie la disponibilité des fichiers de prévision
        max_forecast_hours (int): Nombre maximum d'heures de prévision à vérifier

    Returns:
        pandas.DataFrame: Soit la table d'erreurs, soit les données compilées, soit la table de disponibilité
    """
    logger.info(f"Processing GRIB files for run date: {run_date}")

    # Si demandé, vérifier la disponibilité des fichiers de prévision
    if check_availability:
        # Ensure we have the hour format for availability check
        if len(run_date.split('-')) == 3:
            run_date_with_hour = f"{run_date}-00"
        else:
            run_date_with_hour = run_date

        logger.info("Checking forecast file availability...")
        return check_forecast_files_availability(run_date_with_hour, archive_folder, max_forecast_hours)

    # Étape 1: Récupérer tous les fichiers pour cette date
    try:
        files = get_files_for_run_date(run_date, archive_folder)
    except ValueError as e:
        logger.error(f"Error getting files: {e}")
        return pd.DataFrame()

    if not files:
        logger.warning(f"No files found for run date: {run_date}")
        return pd.DataFrame()

    # Étape 2: Valider chaque fichier
    validation_results = []
    valid_datasets = []

    for filepath in files:
        result = validate_grib_file(filepath)
        validation_results.append({
            'file_name': result['file_name'],
            'variable_check': result['variable_check'],
            'row_check': result['row_check']
        })

        # Si le fichier est valide, garder le dataset
        if result['dataset'] is not None:
            valid_datasets.append((result['file_name'], result['dataset']))

    # Vérifier s'il y a des échecs de validation
    failed_files = [r for r in validation_results if r['variable_check'] == 0 or r['row_check'] == 0]

    if failed_files:
        logger.error(f"Validation failed for {len(failed_files)} files")
        error_df = pd.DataFrame(validation_results)
        return error_df

    # Étape 3: Compiler les données des fichiers valides
    logger.info(f"All {len(files)} files passed validation. Compiling data...")

    compiled_data = []

    for file_name, ds in valid_datasets:
        try:
            # Convertir en DataFrame
            df = ds.to_dataframe()

            # Reset index to get latitude and longitude as columns
            df = df.reset_index()

            # Garder seulement les colonnes requises
            required_columns = ['latitude', 'longitude', 'valid_time', 'tp']

            # Vérifier que toutes les colonnes requises sont présentes
            available_columns = df.columns.tolist()
            missing_columns = [col for col in required_columns if col not in available_columns]

            if missing_columns:
                logger.warning(f"Missing columns in {file_name}: {missing_columns}")
                continue

            # Sélectionner les colonnes requises
            df_filtered = df[required_columns].copy()

            # Ajouter la colonne file_name
            df_filtered['file_name'] = file_name

            # Ajouter la colonne time (extraite du dataset)
            if 'time' in ds.coords:
                df_filtered['time'] = ds.coords['time'].values
            elif 'time' in ds.variables:
                df_filtered['time'] = ds.variables['time'].values
            else:
                logger.warning(f"No time coordinate found in {file_name}")
                df_filtered['time'] = None

            compiled_data.append(df_filtered)
            logger.info(f"✅ Processed {file_name}: {len(df_filtered)} rows")

        except Exception as e:
            logger.error(f"Error processing {file_name}: {e}")
            continue

    if not compiled_data:
        logger.error("No data could be compiled from valid files")
        return pd.DataFrame()

    # Concaténer tous les DataFrames
    final_df = pd.concat(compiled_data, ignore_index=True)

    # Ajouter les colonnes min_date_upload et max_date_upload
    logger.info("Adding upload date range columns...")

    import datetime
    upload_dates = []

    # Collecter les dates de modification de tous les fichiers
    for filepath in files:
        try:
            modification_time = os.path.getmtime(filepath)
            modification_date = datetime.datetime.fromtimestamp(modification_time)
            upload_dates.append(modification_date)
        except Exception as e:
            logger.warning(f"Could not get modification time for {filepath}: {e}")

    if upload_dates:
        min_date_upload = min(upload_dates)
        max_date_upload = max(upload_dates)

        # Format dates to minute precision (no seconds)
        min_date_formatted = min_date_upload.replace(second=0, microsecond=0)
        max_date_formatted = max_date_upload.replace(second=0, microsecond=0)

        # Ajouter ces colonnes à toutes les lignes
        final_df['min_date_upload'] = min_date_formatted
        final_df['max_date_upload'] = max_date_formatted

        logger.info(f"✅ Upload date range: {min_date_formatted} to {max_date_formatted}")
    else:
        final_df['min_date_upload'] = None
        final_df['max_date_upload'] = None
        logger.warning("Could not determine upload date range")

    # Réorganiser les colonnes dans l'ordre spécifié
    column_order = ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']
    final_df = final_df[column_order]

    logger.info(f"✅ Successfully compiled data: {len(final_df)} total rows from {len(valid_datasets)} files")

    return final_df

def process_and_export_grib_files(run_date, archive_folder="Archive", output_file=None, check_availability=False, max_forecast_hours=72, include_enhanced_columns=False, upload_to_s3_bucket=True):
    """
    Traite les fichiers GRIB pour une date donnée et exporte automatiquement en CSV.

    Args:
        run_date (str): Date de run au format "YYYY-MM-DD-HH"
        archive_folder (str): Dossier contenant les fichiers GRIB
        output_file (str): Nom du fichier CSV de sortie (optionnel)
        check_availability (bool): Si True, vérifie la disponibilité des fichiers de prévision
        max_forecast_hours (int): Nombre maximum d'heures de prévision à vérifier
        include_enhanced_columns (bool): Si True, ajoute les colonnes enrichies (precipitation, timezone, dates) - DEFAULT: False pour performance
        upload_to_s3_bucket (bool): Si True, upload le fichier CSV vers S3 - DEFAULT: True

    Returns:
        pandas.DataFrame: Les données traitées
        str: Chemin du fichier CSV créé
    """
    logger.info(f"Processing and exporting GRIB files for run date: {run_date}")

    # Traiter les fichiers
    result = process_grib_files_for_run(run_date, archive_folder, check_availability, max_forecast_hours)

    if result.empty:
        logger.warning("No data to export")
        return result, None

    # Ajouter les colonnes enrichies si demandé et si ce sont des données compilées
    if include_enhanced_columns and 'variable_check' not in result.columns and 'is_present' not in result.columns:
        logger.info("Adding enhanced columns (precipitation, timezone, date components)...")
        try:
            result = add_enhanced_columns(result)
            logger.info(f"✅ Enhanced columns added successfully")
        except Exception as e:
            logger.error(f"❌ Error adding enhanced columns: {e}")
            logger.info("Proceeding with basic data export...")

    # Déterminer le nom du fichier de sortie
    if output_file is None:
        if 'variable_check' in result.columns:
            # C'est une table d'erreurs
            output_file = f"grib_validation_errors_{run_date.replace(':', '-')}.csv"
        elif 'is_present' in result.columns:
            # C'est une table de disponibilité
            output_file = f"grib_availability_check_{run_date.replace(':', '-')}.csv"
        else:
            # C'est une table de données compilées
            base_filename = f"grib_compiled_data_{run_date.replace(':', '-')}"

            # Ajouter la date d'upload si disponible
            if 'max_date_upload' in result.columns and not result.empty:
                max_upload_date = result['max_date_upload'].iloc[0]
                if max_upload_date is not None:
                    # Format: YYYY-MM-DD-HH-MM
                    upload_str = max_upload_date.strftime('%Y-%m-%d-%H-%M')
                    output_file = f"{base_filename}_upload_{upload_str}.csv"
                else:
                    output_file = f"{base_filename}.csv"
            else:
                output_file = f"{base_filename}.csv"

    # Exporter en CSV (format français)
    try:
        # Export in French CSV format
        french_csv_file = export_to_french_csv(result, output_file)
        logger.info(f"✅ Data exported to: {french_csv_file}")
        logger.info(f"📊 Exported {len(result)} rows")
        if include_enhanced_columns and 'precipitation' in result.columns:
            logger.info(f"📊 Enhanced data includes: precipitation, timezone conversion, and date components")

        # Upload to S3 if requested and not an error/availability check
        if upload_to_s3_bucket and 'variable_check' not in result.columns and 'is_present' not in result.columns:
            logger.info(f"🔄 Uploading to S3 bucket: {S3_UPLOAD_BUCKET}")
            s3_success = upload_to_s3(french_csv_file, os.path.basename(french_csv_file))
            if s3_success:
                logger.info(f"✅ Successfully uploaded to S3: s3://{S3_UPLOAD_BUCKET}/{os.path.basename(french_csv_file)}")
            else:
                logger.warning(f"⚠️ S3 upload failed, but local file saved: {french_csv_file}")

        return result, french_csv_file
    except Exception as e:
        logger.error(f"❌ Error exporting to CSV: {e}")
        return result, None

def process_single_grib_file(file_path, output_file=None):
    """
    Traite un seul fichier GRIB et l'exporte en CSV pour vérification manuelle.

    Args:
        file_path (str): Chemin vers le fichier GRIB
        output_file (str): Nom du fichier CSV de sortie (optionnel)

    Returns:
        pandas.DataFrame: Les données du fichier
        str: Chemin du fichier CSV créé
        dict: Informations de validation
    """
    logger.info(f"Processing single GRIB file: {file_path}")

    # Valider le fichier
    validation_result = validate_grib_file(file_path)

    file_name = os.path.basename(file_path)

    # Informations de validation
    validation_info = {
        'file_name': validation_result['file_name'],
        'variable_check_passed': validation_result['variable_check'] == 1,
        'row_check_passed': validation_result['row_check'] == 1,
        'dataset_loaded': validation_result['dataset'] is not None
    }

    if validation_result['dataset'] is None:
        logger.error(f"❌ Could not load file: {file_name}")
        return pd.DataFrame(), None, validation_info

    try:
        # Convertir en DataFrame
        ds = validation_result['dataset']
        df = ds.to_dataframe()
        df = df.reset_index()

        # Ajouter des informations supplémentaires pour l'analyse manuelle
        df['file_name'] = file_name

        # Ajouter les colonnes d'upload date pour un seul fichier
        import datetime
        modification_time = os.path.getmtime(file_path)
        modification_date = datetime.datetime.fromtimestamp(modification_time)

        # Format date to minute precision (no seconds)
        modification_date_formatted = modification_date.replace(second=0, microsecond=0)

        # Pour un seul fichier, min et max sont identiques
        df['min_date_upload'] = modification_date_formatted
        df['max_date_upload'] = modification_date_formatted

        # Ajouter la colonne time si disponible
        if 'time' in ds.coords:
            df['time'] = ds.coords['time'].values
        elif 'time' in ds.variables:
            df['time'] = ds.variables['time'].values

        # Réorganiser les colonnes pour une meilleure lisibilité
        important_cols = ['latitude', 'longitude', 'time', 'valid_time', 'tp', 'file_name', 'min_date_upload', 'max_date_upload']
        other_cols = [col for col in df.columns if col not in important_cols]
        df = df[important_cols + other_cols]

        # Déterminer le nom du fichier de sortie
        if output_file is None:
            base_name = os.path.splitext(file_name)[0]
            output_file = f"single_file_check_{base_name}.csv"

        # Exporter en CSV
        df.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"✅ Single file data exported to: {output_file}")
        logger.info(f"📊 File contains {len(df)} rows and {len(df.columns)} columns")

        # Afficher un résumé des données
        logger.info(f"📋 Data summary for {file_name}:")
        logger.info(f"   - Latitude range: {df['latitude'].min():.2f} to {df['latitude'].max():.2f}")
        logger.info(f"   - Longitude range: {df['longitude'].min():.2f} to {df['longitude'].max():.2f}")
        logger.info(f"   - TP (precipitation) range: {df['tp'].min():.6f} to {df['tp'].max():.6f}")
        if 'valid_time' in df.columns:
            logger.info(f"   - Valid time: {df['valid_time'].iloc[0]}")

        return df, output_file, validation_info

    except Exception as e:
        logger.error(f"❌ Error processing single file {file_name}: {e}")
        return pd.DataFrame(), None, validation_info

def add_enhanced_columns(df):
    """
    Add enhanced columns to the GRIB data DataFrame:
    - Decumulated precipitation (rounded to 1 decimal)
    - Paris timezone conversion
    - Run date components
    - Forecast date components

    Args:
        df (pandas.DataFrame): DataFrame from process_grib_files_for_run

    Returns:
        pandas.DataFrame: Enhanced DataFrame with additional columns
    """
    import pandas as pd
    from datetime import datetime

    if df.empty:
        logger.warning("Empty DataFrame provided to add_enhanced_columns")
        return df

    logger.info("Adding enhanced columns to DataFrame...")

    # Make a copy to avoid modifying the original
    enhanced_df = df.copy()

    # 1. Decumulate tp variable by latitude and longitude (OPTIMIZED)
    logger.info("Decumulating precipitation data...")

    # Optimize: Sort only once and use more efficient operations
    enhanced_df = enhanced_df.sort_values(['latitude', 'longitude', 'valid_time'])

    # Optimize: Use transform instead of groupby().diff() for better performance
    enhanced_df['precipitation'] = enhanced_df.groupby(['latitude', 'longitude'])['tp'].transform(
        lambda x: x.diff().fillna(x)
    )

    # Multiply by 1000 and round to 1 decimal place in one operation
    enhanced_df['precipitation'] = (enhanced_df['precipitation'] * 1000).round(1)

    logger.info("✅ Precipitation decumulation completed")

    # 2. Convert valid_time to Paris timezone (OPTIMIZED)
    logger.info("Converting timezone from UTC to Paris...")

    # Optimize: Vectorized timezone operations
    if not pd.api.types.is_datetime64_any_dtype(enhanced_df['valid_time']):
        enhanced_df['valid_time'] = pd.to_datetime(enhanced_df['valid_time'])

    # Optimize: Use vectorized operations for timezone conversion
    if enhanced_df['valid_time'].dt.tz is None:
        enhanced_df['valid_time'] = enhanced_df['valid_time'].dt.tz_localize('UTC')

    # Optimize: Direct conversion without intermediate timezone object
    enhanced_df['valid_time_fr'] = enhanced_df['valid_time'].dt.tz_convert('Europe/Paris')

    logger.info("✅ Timezone conversion completed")

    # 3. Extract run date components from filename (OPTIMIZED)
    logger.info("Extracting run date components...")

    # Optimize: Extract from first filename only (all files have same run date)
    first_filename = enhanced_df['file_name'].iloc[0]
    parsed = parse_ecmwf_filename(first_filename)

    if parsed:
        current_year = datetime.now().year
        run_month = int(parsed['run_month'])
        run_day = int(parsed['run_day'])
        run_hour = int(parsed['run_hour'])

        # Optimize: Assign to all rows at once (vectorized)
        enhanced_df['run_year'] = current_year
        enhanced_df['run_month'] = run_month
        enhanced_df['run_day'] = run_day
        enhanced_df['run_hour'] = run_hour
    else:
        enhanced_df['run_year'] = None
        enhanced_df['run_month'] = None
        enhanced_df['run_day'] = None
        enhanced_df['run_hour'] = None

    logger.info("✅ Run date components extracted")

    # 4. Extract forecast date components from valid_time_fr (OPTIMIZED)
    logger.info("Extracting forecast date components...")

    # Optimize: Vectorized date component extraction
    dt_components = enhanced_df['valid_time_fr'].dt
    enhanced_df['forecasted_year'] = dt_components.year
    enhanced_df['forecasted_month'] = dt_components.month
    enhanced_df['forecasted_day'] = dt_components.day
    enhanced_df['forecasted_hour'] = dt_components.hour

    logger.info("✅ Forecast date components extracted")

    # 5. Round latitude and longitude to 1 decimal place
    logger.info("Rounding coordinates to 1 decimal place...")
    enhanced_df['latitude'] = enhanced_df['latitude'].round(1)
    enhanced_df['longitude'] = enhanced_df['longitude'].round(1)
    logger.info("✅ Coordinates rounded to 1 decimal place")

    # 6. Filter data to include only forecasts for next 2 days (run_day and run_day+1)
    logger.info("Filtering data to include only forecasts for next 2 days...")

    if parsed and 'valid_time_fr' in enhanced_df.columns:
        from datetime import datetime, timedelta

        # Create the run date from parsed components
        run_date = datetime(current_year, run_month, run_day)
        next_day = run_date + timedelta(days=1)

        # Extract date from valid_time_fr for comparison
        enhanced_df['forecast_date'] = enhanced_df['valid_time_fr'].dt.date

        # Filter to include only run_date and next_day
        target_dates = [run_date.date(), next_day.date()]
        enhanced_df = enhanced_df[enhanced_df['forecast_date'].isin(target_dates)]

        # Remove the temporary column
        enhanced_df = enhanced_df.drop('forecast_date', axis=1)

        logger.info(f"✅ Filtered to {len(enhanced_df)} rows for dates: {target_dates[0]} and {target_dates[1]}")
    else:
        logger.warning("Could not filter by forecast dates - missing run date info or valid_time_fr")

    # 7. Reorder columns for better readability
    column_order = [
        'latitude', 'longitude', 'time', 'valid_time', 'valid_time_fr',
        'tp', 'precipitation', 'file_name', 'min_date_upload', 'max_date_upload',
        'run_year', 'run_month', 'run_day', 'run_hour',
        'forecasted_year', 'forecasted_month', 'forecasted_day', 'forecasted_hour'
    ]

    # Only include columns that exist
    available_columns = [col for col in column_order if col in enhanced_df.columns]
    other_columns = [col for col in enhanced_df.columns if col not in available_columns]
    final_columns = available_columns + other_columns

    enhanced_df = enhanced_df[final_columns]

    logger.info(f"✅ Enhanced DataFrame created with {len(enhanced_df)} rows and {len(enhanced_df.columns)} columns")

    return enhanced_df

# Example usage and testing functions
if __name__ == "__main__":
    # Test 1: Check file availability for a specific run
    print("=== Test 1: Checking forecast file availability ===")
    test_run_date = "2025-04-26-00"  # Run date with hour

    # Check availability for first 24 hours only (for faster testing)
    availability_result, availability_csv = process_and_export_grib_files(
        test_run_date,
        check_availability=True,
        max_forecast_hours=24
    )

    if availability_csv:
        print(f"✅ Availability check exported to: {availability_csv}")
        print(f"📊 Result shape: {availability_result.shape}")
        print(f"📋 Columns: {availability_result.columns.tolist()}")
        if len(availability_result) > 0:
            print(f"📋 Sample results:")
            print(availability_result.head())
            print(f"📊 Summary: {availability_result['is_present'].sum()}/{len(availability_result)} files present")

    print("\n" + "="*60 + "\n")

    # Test 2: Process and export all files for a date (basic mode for speed)
    print("=== Test 2: Processing all files for a run date (basic mode) ===")
    test_date = "2025-04-26-00"  # Using new format with hour
    result, csv_file = process_and_export_grib_files(test_date, include_enhanced_columns=False)

    if csv_file:
        print(f"✅ Data exported to: {csv_file}")
        print(f"📊 Result shape: {result.shape}")
        print(f"📋 Columns: {result.columns.tolist()}")

    print("\n" + "="*60 + "\n")

    # Test 3: Test ECMWF filename parsing
    print("=== Test 3: Testing ECMWF filename parsing ===")
    test_filenames = [
        "A1D04260000042602001",
        "A1S04261800042618011",
        "A1D04270000042700011"
    ]

    for filename in test_filenames:
        parsed = parse_ecmwf_filename(filename)
        if parsed:
            print(f"📁 {filename}:")
            print(f"   Run: {parsed['run_month']}/{parsed['run_day']} at {parsed['run_hour']}:{parsed['run_minute']}")
            print(f"   Forecast: {parsed['forecast_month']}/{parsed['forecast_day']} at {parsed['forecast_hour']}:{parsed['forecast_minute']}")
            print(f"   Stream: {parsed['dissemination_stream']}{parsed['stream_indicator']}")
        else:
            print(f"❌ Could not parse: {filename}")

    print("\n" + "="*60 + "\n")

    # Test 3.5: Test Enhanced Columns Function
    print("=== Test 3.5: Testing Enhanced Columns Function ===")
    test_enhanced_date = "2025-04-28-00"

    print(f"Testing enhanced columns for: {test_enhanced_date}")

    # Get basic data first
    basic_data = process_grib_files_for_run(test_enhanced_date)

    if not basic_data.empty and 'variable_check' not in basic_data.columns:
        print(f"✅ Basic data loaded: {basic_data.shape[0]:,} rows")

        # Test with small subset for speed
        test_subset = basic_data.head(1000)
        print(f"🔄 Testing enhanced function with {len(test_subset)} rows...")

        try:
            enhanced_data = add_enhanced_columns(test_subset)

            print(f"✅ Enhanced processing completed!")
            print(f"📊 Enhanced data shape: {enhanced_data.shape}")
            print(f"📋 Enhanced columns: {list(enhanced_data.columns)}")

            # Verify key features
            print("\n🔍 Verification:")

            # Check precipitation
            precip_stats = enhanced_data['precipitation'].describe()
            print(f"   Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm")

            # Check timezone conversion
            sample_utc = enhanced_data['valid_time'].iloc[0]
            sample_paris = enhanced_data['valid_time_fr'].iloc[0]
            print(f"   Timezone: {sample_utc} → {sample_paris}")

            # Check date components
            sample_row = enhanced_data.iloc[0]
            print(f"   Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}")
            print(f"   Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}")

            # Test decumulation for one grid point
            sample_lat = enhanced_data['latitude'].iloc[0]
            sample_lon = enhanced_data['longitude'].iloc[0]

            grid_data = enhanced_data[
                (enhanced_data['latitude'] == sample_lat) &
                (enhanced_data['longitude'] == sample_lon)
            ].sort_values('valid_time')

            print(f"   Grid point test: lat={sample_lat:.2f}, lon={sample_lon:.2f} ({len(grid_data)} time steps)")

            if len(grid_data) > 1:
                print("   Decumulation sample:")
                for _, row in grid_data.head(3).iterrows():
                    time_str = row['valid_time_fr'].strftime('%H:%M')
                    print(f"     {time_str}: TP={row['tp']:.6f}, Precipitation={row['precipitation']:.1f} mm")

            # Export test
            output_file = f"test_enhanced_{test_enhanced_date.replace(':', '-')}.csv"
            enhanced_data.to_csv(output_file, index=False)
            print(f"✅ Test data exported to: {output_file}")

        except Exception as e:
            print(f"❌ Enhanced function test failed: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ No valid basic data for enhanced testing")

    print("\n" + "="*60 + "\n")

    # Test 4: Process a single file for manual check
    print("=== Test 4: Processing single file for manual check ===")
    single_file = "Archive/A1D04260000042602001"  # Example file

    if os.path.exists(single_file):
        df_single, csv_single, validation = process_single_grib_file(single_file)

        if csv_single:
            print(f"✅ Single file exported to: {csv_single}")
            print(f"📊 Validation results: {validation}")
            print(f"📋 Data shape: {df_single.shape}")
            if len(df_single) > 0:
                print(f"📋 First few rows:")
                print(df_single.head())
    else:
        print(f"❌ Test file not found: {single_file}")
        print("Available files in Archive:")
        if os.path.exists("Archive"):
            files = [f for f in os.listdir("Archive") if not f.endswith('.idx')][:5]
            for f in files:
                print(f"   - {f}")
            if len(files) > 0:
                test_file = os.path.join("Archive", files[0])
                print(f"\nTrying with first available file: {test_file}")
                df_single, csv_single, validation = process_single_grib_file(test_file)
                if csv_single:
                    print(f"✅ Single file exported to: {csv_single}")
                    print(f"📊 Validation results: {validation}")

    # Final test: Quick availability check
    print("\n=== Final Test: Quick Availability Check ===")
    result, csv_file = process_and_export_grib_files(
        "2025-04-28-00",
        check_availability=True,
        max_forecast_hours=12
    )

    print(f"Availability check exported to: {csv_file}")
    print(f"Files present: {result['is_present'].sum()}/{len(result)}")
    print(result.head())