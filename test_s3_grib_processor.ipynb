{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🌟 S3 GRIB Processor Test Notebook\n", "\n", "This notebook tests the complete S3 integration for ECMWF GRIB data processing.\n", "\n", "## Features Tested:\n", "- ✅ S3 connectivity and bucket access\n", "- ✅ File discovery and listing\n", "- ✅ Individual file processing\n", "- ✅ Complete run date processing\n", "- ✅ Enhanced columns generation\n", "- ✅ CSV export with S3 metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import S3 GRIB processor functions\n", "from grib_processor_S3 import (\n", "    list_s3_objects,\n", "    get_s3_files_for_run_date,\n", "    process_s3_grib_files_for_run,\n", "    process_and_export_s3_grib_files,\n", "    process_single_s3_grib_file,\n", "    get_s3_object_metadata,\n", "    test_s3_file_access,\n", "    s3_client,\n", "    bucket_name\n", ")\n", "\n", "# Import additional libraries\n", "import pandas as pd\n", "import os\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"📦 S3 Bucket: {bucket_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔗 Test 1: S3 Connectivity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🔄 Testing S3 connectivity...\")\n", "\n", "try:\n", "    # List first 20 objects to test connectivity\n", "    all_objects = list_s3_objects()\n", "    \n", "    if all_objects:\n", "        print(f\"✅ S3 connectivity successful!\")\n", "        print(f\"📊 Total objects in bucket: {len(all_objects)}\")\n", "        \n", "        print(\"\\n📋 First 20 objects:\")\n", "        for i, obj in enumerate(all_objects[:20]):\n", "            print(f\"   {i+1:2d}. {obj}\")\n", "        \n", "        if len(all_objects) > 20:\n", "            print(f\"   ... and {len(all_objects) - 20} more objects\")\n", "            \n", "        # Test metadata for first object\n", "        if all_objects:\n", "            first_obj = all_objects[0]\n", "            metadata = get_s3_object_metadata(first_obj)\n", "            if metadata:\n", "                print(f\"\\n📋 Sample metadata for '{first_obj}':\")\n", "                print(f\"   Size: {metadata['size']:,} bytes\")\n", "                print(f\"   Last Modified: {metadata['last_modified']}\")\n", "                print(f\"   ETag: {metadata['etag']}\")\n", "    else:\n", "        print(\"⚠️ S3 bucket appears to be empty\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ S3 connectivity failed: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📅 Test 2: File Discovery for Specific Run Date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test file discovery for a specific run date\n", "test_run_date = \"2025-04-28-00\"\n", "print(f\"🔍 Searching for files with run date: {test_run_date}\")\n", "\n", "try:\n", "    matching_files = get_s3_files_for_run_date(test_run_date)\n", "    \n", "    if matching_files:\n", "        print(f\"✅ Found {len(matching_files)} matching files:\")\n", "        \n", "        for i, file_key in enumerate(matching_files[:10]):\n", "            print(f\"   {i+1:2d}. {file_key}\")\n", "        \n", "        if len(matching_files) > 10:\n", "            print(f\"   ... and {len(matching_files) - 10} more files\")\n", "            \n", "        # Test access to first file\n", "        if matching_files:\n", "            first_file = matching_files[0]\n", "            print(f\"\\n🔄 Testing access to first file: {first_file}\")\n", "            accessible = test_s3_file_access(first_file)\n", "            if accessible:\n", "                print(\"✅ File is accessible\")\n", "            else:\n", "                print(\"❌ File access failed\")\n", "    else:\n", "        print(f\"❌ No files found for run date: {test_run_date}\")\n", "        print(\"💡 Try a different run date or check the ECMWF filename convention\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error during file discovery: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔬 Test 3: Single File Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test processing a single file from S3\n", "if 'matching_files' in locals() and matching_files:\n", "    test_file = matching_files[0]  # Use first file from previous test\n", "    print(f\"🔬 Testing single file processing: {test_file}\")\n", "    \n", "    try:\n", "        df_single, csv_file, validation = process_single_s3_grib_file(test_file)\n", "        \n", "        print(f\"\\n📊 Processing Results:\")\n", "        print(f\"   Validation passed: {validation['variable_check_passed'] and validation['row_check_passed']}\")\n", "        print(f\"   Dataset loaded: {validation['dataset_loaded']}\")\n", "        \n", "        if not df_single.empty:\n", "            print(f\"   Data shape: {df_single.shape}\")\n", "            print(f\"   Columns: {list(df_single.columns)}\")\n", "            \n", "            if csv_file:\n", "                print(f\"   CSV exported to: {csv_file}\")\n", "                print(f\"   File size: {os.path.getsize(csv_file):,} bytes\")\n", "            \n", "            # Show sample data\n", "            print(f\"\\n📋 Sample data (first 5 rows):\")\n", "            display_cols = ['latitude', 'longitude', 'valid_time', 'tp', 'file_name']\n", "            available_cols = [col for col in display_cols if col in df_single.columns]\n", "            print(df_single[available_cols].head())\n", "            \n", "            # Basic statistics\n", "            print(f\"\\n📈 Basic Statistics:\")\n", "            print(f\"   Latitude range: {df_single['latitude'].min():.2f} to {df_single['latitude'].max():.2f}\")\n", "            print(f\"   Longitude range: {df_single['longitude'].min():.2f} to {df_single['longitude'].max():.2f}\")\n", "            print(f\"   TP range: {df_single['tp'].min():.6f} to {df_single['tp'].max():.6f}\")\n", "        else:\n", "            print(\"❌ No data returned from single file processing\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error during single file processing: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "else:\n", "    print(\"⚠️ No files available for single file testing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Test 4: Complete Run Date Processing (Basic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test complete processing for a run date (basic mode)\n", "print(f\"🚀 Testing complete run date processing: {test_run_date}\")\n", "print(\"📋 Mode: Basic (no enhanced columns for speed)\")\n", "\n", "try:\n", "    # Process basic data\n", "    start_time = datetime.now()\n", "    basic_df = process_s3_grib_files_for_run(test_run_date)\n", "    processing_time = (datetime.now() - start_time).total_seconds()\n", "    \n", "    if not basic_df.empty and 'variable_check' not in basic_df.columns:\n", "        print(f\"✅ Basic processing completed in {processing_time:.1f} seconds\")\n", "        print(f\"📊 Data shape: {basic_df.shape}\")\n", "        print(f\"📋 Columns: {list(basic_df.columns)}\")\n", "        \n", "        # Show sample data\n", "        print(f\"\\n📋 Sample basic data:\")\n", "        print(basic_df.head())\n", "        \n", "        # Check upload date columns\n", "        if 'min_date_upload' in basic_df.columns:\n", "            min_upload = basic_df['min_date_upload'].iloc[0]\n", "            max_upload = basic_df['max_date_upload'].iloc[0]\n", "            print(f\"\\n📅 Upload date range: {min_upload} to {max_upload}\")\n", "        \n", "        # Basic statistics\n", "        print(f\"\\n📈 Data Statistics:\")\n", "        print(f\"   Total rows: {len(basic_df):,}\")\n", "        print(f\"   Unique files: {basic_df['file_name'].nunique()}\")\n", "        print(f\"   Latitude range: {basic_df['latitude'].min():.1f} to {basic_df['latitude'].max():.1f}\")\n", "        print(f\"   Longitude range: {basic_df['longitude'].min():.1f} to {basic_df['longitude'].max():.1f}\")\n", "        print(f\"   TP range: {basic_df['tp'].min():.6f} to {basic_df['tp'].max():.6f}\")\n", "        \n", "    else:\n", "        print(\"❌ No valid data returned or validation errors occurred\")\n", "        if not basic_df.empty:\n", "            print(\"📋 Validation results:\")\n", "            print(basic_df)\n", "            \n", "except Exception as e:\n", "    print(f\"❌ Error during basic processing: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⭐ Test 5: Enhanced Processing with All Features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test enhanced processing with all features\n", "print(f\"⭐ Testing enhanced processing with all features: {test_run_date}\")\n", "print(\"📋 Mode: Enhanced (precipitation, timezone, date components)\")\n", "\n", "try:\n", "    # Process with enhanced features\n", "    start_time = datetime.now()\n", "    enhanced_df, csv_file = process_and_export_s3_grib_files(\n", "        run_date=test_run_date,\n", "        include_enhanced_columns=True\n", "    )\n", "    processing_time = (datetime.now() - start_time).total_seconds()\n", "    \n", "    if not enhanced_df.empty and csv_file:\n", "        print(f\"✅ Enhanced processing completed in {processing_time:.1f} seconds\")\n", "        print(f\"📊 Enhanced data shape: {enhanced_df.shape}\")\n", "        print(f\"📋 Enhanced columns: {list(enhanced_df.columns)}\")\n", "        print(f\"📤 CSV exported to: {csv_file}\")\n", "        print(f\"📊 File size: {os.path.getsize(csv_file):,} bytes ({os.path.getsize(csv_file)/1024/1024:.1f} MB)\")\n", "        \n", "        # Show enhanced sample data\n", "        print(f\"\\n📋 Sample enhanced data:\")\n", "        enhanced_cols = ['latitude', 'longitude', 'valid_time_fr', 'tp', 'precipitation', \n", "                        'run_month', 'run_day', 'forecasted_hour']\n", "        available_enhanced = [col for col in enhanced_cols if col in enhanced_df.columns]\n", "        print(enhanced_df[available_enhanced].head())\n", "        \n", "        # Enhanced verification\n", "        print(f\"\\n🔍 Enhanced Features Verification:\")\n", "        \n", "        # Check precipitation\n", "        if 'precipitation' in enhanced_df.columns:\n", "            precip_stats = enhanced_df['precipitation'].describe()\n", "            print(f\"   📊 Precipitation: min={precip_stats['min']:.1f}, max={precip_stats['max']:.1f} mm\")\n", "        \n", "        # Check timezone conversion\n", "        if 'valid_time_fr' in enhanced_df.columns:\n", "            sample_utc = enhanced_df['valid_time'].iloc[0]\n", "            sample_paris = enhanced_df['valid_time_fr'].iloc[0]\n", "            print(f\"   🌍 Timezone: {sample_utc} → {sample_paris}\")\n", "        \n", "        # Check date components\n", "        if 'run_year' in enhanced_df.columns:\n", "            sample_row = enhanced_df.iloc[0]\n", "            print(f\"   📅 Run date: {sample_row['run_year']}-{sample_row['run_month']:02d}-{sample_row['run_day']:02d}\")\n", "            print(f\"   📅 Forecast date: {sample_row['forecasted_year']}-{sample_row['forecasted_month']:02d}-{sample_row['forecasted_day']:02d}\")\n", "        \n", "        # Check S3 metadata\n", "        if 'min_date_upload' in enhanced_df.columns:\n", "            print(f\"   📅 S3 upload range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}\")\n", "        \n", "        print(f\"\\n🎉 All enhanced features working correctly!\")\n", "        \n", "    else:\n", "        print(\"❌ Enhanced processing failed\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error during enhanced processing: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}