# GRIB Processor Quick Reference Guide

## 📁 Files Created
- **`grib_processor.py`** - Main Python module with all functions
- **`grib_processor_testing.ipynb`** - Interactive Jupyter notebook for testing
- **`GRIB_Processor_Quick_Reference.md`** - This reference guide

## 🚀 Quick Start

### 1. Basic Usage
```python
from grib_processor import process_and_export_grib_files

# Process all files for a run date and export to CSV
result, csv_file = process_and_export_grib_files("2025-04-26-00")
print(f"Data exported to: {csv_file}")
```

### 2. Check Forecast Availability
```python
# Check which forecast files are missing for 72-hour forecast
result, csv_file = process_and_export_grib_files(
    "2025-04-26-00", 
    check_availability=True, 
    max_forecast_hours=72
)
```

### 3. Process Single File
```python
from grib_processor import process_single_grib_file

# Process one file for manual inspection
df, csv_file, validation = process_single_grib_file("Archive/A1D04260000042602001")
```

## 🔧 Main Functions

### `parse_ecmwf_filename(filename)`
Parse ECMWF filename according to convention: `ccSMMDDHHIImmddhhiiE`
- **Input**: Filename string
- **Output**: Dictionary with parsed components or None

### `get_files_for_run_date(run_date, archive_folder="Archive")`
Find all GRIB files for a specific run date
- **Input**: Run date ("YYYY-MM-DD" or "YYYY-MM-DD-HH")
- **Output**: List of file paths

### `check_forecast_files_availability(run_date, archive_folder="Archive", max_forecast_hours=72)`
Check availability of expected forecast files
- **Input**: Run date with hour ("YYYY-MM-DD-HH")
- **Output**: DataFrame with availability information

### `process_grib_files_for_run(run_date, archive_folder="Archive", check_availability=False, max_forecast_hours=72)`
Main processing function
- **Input**: Run date, options for availability check
- **Output**: DataFrame (compiled data, error table, or availability table)

### `process_and_export_grib_files(run_date, archive_folder="Archive", output_file=None, check_availability=False, max_forecast_hours=72)`
Process and automatically export to CSV
- **Input**: Run date and export options
- **Output**: (DataFrame, CSV filename)

### `process_single_grib_file(file_path, output_file=None)`
Process single file for manual inspection
- **Input**: File path
- **Output**: (DataFrame, CSV filename, validation info)

## 📊 Input Formats

### Date Formats
- **`"YYYY-MM-DD-HH"`** - Date with specific hour

### Examples
- `"2025-04-26-18"` → April 26, 2025 at 18:00

## 📋 Output Types

### 1. Compiled Data (Success)
Columns: `latitude`, `longitude`, `time`, `valid_time`, `tp`, `file_name`

### 2. Validation Errors
Columns: `file_name`, `variable_check`, `row_check`

### 3. Availability Check
Columns: `run_date`, `file_name`, `forecasted_date`, `forecasted_hour`, `is_present`

## 🔍 ECMWF File Naming Convention

Format: `ccSMMDDHHIImmddhhiiE`

- **cc**: Dissemination stream (e.g., "A1")
- **S**: Stream indicator (e.g., "D", "S")
- **MMDDHHII**: Run date/time (Month, Day, Hour, Minute)
- **mmddhhii**: Forecast valid date/time
- **E**: Experiment version (e.g., "001")

### Example
`A1D04260000042602001`:
- Run: April 26 at 00:00
- Forecast: April 26 at 02:00
- Stream: A1D (Deterministic)

## ✅ Validation Criteria

### Required Variables
- `latitude`
- `longitude` 
- `valid_time`
- `tp` (total precipitation)

### Row Count
- Must contain exactly **16,952 rows**

## 📤 CSV Export Files

### Automatic Naming
- **Compiled data**: `grib_compiled_data_YYYY-MM-DD-HH.csv`
- **Validation errors**: `grib_validation_errors_YYYY-MM-DD-HH.csv`
- **Availability check**: `grib_availability_check_YYYY-MM-DD-HH.csv`
- **Single file**: `single_file_check_FILENAME.csv`

## 🧪 Testing with Jupyter Notebook

1. Open `grib_processor_testing.ipynb`
2. Run cells step by step to test each function
3. Modify test parameters to match your data
4. Use the custom testing section for experiments

### Key Testing Sections
1. **Setup and Imports** - Load functions and check environment
2. **ECMWF Filename Parsing** - Test filename parsing
3. **File Discovery** - Find files for run dates
4. **Availability Check** - Check forecast file availability
5. **Single File Validation** - Test individual file processing
6. **Complete Pipeline** - Test full processing workflow
7. **CSV Export** - Test export functionality
8. **Custom Testing** - Your own experiments

## 🚨 Common Issues

### File Not Found
- Check that Archive folder exists
- Verify file naming matches ECMWF convention
- Ensure run date format is correct

### Validation Failures
- Check that files contain required variables
- Verify row count is exactly 16,952
- Ensure files are valid GRIB format

### Memory Issues
- Process smaller date ranges
- Use single file processing for large files
- Monitor system memory usage

## 💡 Tips

1. **Start small**: Test with a few files before processing large datasets
2. **Check availability first**: Use availability check to identify missing files
3. **Validate samples**: Process single files to verify data quality
4. **Monitor logs**: Enable logging to track processing progress
5. **Backup data**: Keep original files safe before processing

## 📞 Support

For issues or questions:
1. Check the Jupyter notebook examples
2. Review function documentation in `grib_processor.py`
3. Test with single files first to isolate problems
4. Check log messages for detailed error information
