#!/usr/bin/env python3
"""
Test script for the new upload date format and filename
"""

from grib_processor import process_and_export_grib_files
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_new_date_format():
    """Test the new upload date format and filename functionality"""
    
    print("🔍 Testing New Upload Date Format and Filename")
    print("=" * 60)
    
    run_date = '2025-04-28-00'
    print(f"📅 Testing with run date: {run_date}")
    
    # Process with basic mode to test the new columns and filename
    result, csv_file = process_and_export_grib_files(run_date, include_enhanced_columns=False)
    
    if csv_file:
        print(f"\n✅ Data exported to: {csv_file}")
        print(f"📊 Data shape: {result.shape}")
        print(f"📋 Columns: {list(result.columns)}")
        
        # Check if upload date columns are present
        if 'min_date_upload' in result.columns and 'max_date_upload' in result.columns:
            print(f"\n✅ Upload date columns added successfully!")
            
            # Show upload date range
            min_date = result['min_date_upload'].iloc[0]
            max_date = result['max_date_upload'].iloc[0]
            print(f"📊 Upload date range:")
            print(f"   Min: {min_date}")
            print(f"   Max: {max_date}")
            
            # Check filename format
            if '_upload_' in csv_file:
                print(f"\n✅ Filename includes upload date!")
                print(f"📁 Filename format: {csv_file}")
                
                # Extract upload date from filename
                upload_part = csv_file.split('_upload_')[1].replace('.csv', '')
                print(f"📅 Upload date in filename: {upload_part}")
                
                # Expected format: YYYY-MM-DD-HH-MM
                expected_format = max_date.strftime('%Y-%m-%d-%H-%M')
                print(f"📅 Expected format: {expected_format}")
                
                if upload_part == expected_format:
                    print(f"✅ Filename format matches expected!")
                else:
                    print(f"❌ Filename format mismatch")
                    
            else:
                print(f"❌ Filename does not include upload date: {csv_file}")
                
            # Show date format (should be without seconds)
            print(f"\n📅 Date format verification:")
            print(f"   Min date seconds: {min_date.second}")
            print(f"   Max date seconds: {max_date.second}")
            print(f"   Min date microseconds: {min_date.microsecond}")
            print(f"   Max date microseconds: {max_date.microsecond}")
            
            if min_date.second == 0 and max_date.second == 0 and min_date.microsecond == 0 and max_date.microsecond == 0:
                print(f"✅ Dates formatted correctly (no seconds/microseconds)")
            else:
                print(f"❌ Dates still include seconds or microseconds")
                
            # Show sample data
            print(f"\n📋 Sample data with upload dates:")
            sample_cols = ['file_name', 'min_date_upload', 'max_date_upload']
            sample_data = result[sample_cols].drop_duplicates().head(3)
            print(sample_data)
            
        else:
            print(f"❌ Upload date columns not found")
            print(f"Available columns: {list(result.columns)}")
            
    else:
        print("❌ No CSV file created")
    
    print(f"\n🎉 Test completed!")
    print(f"\n💡 Expected filename format:")
    print(f"   grib_compiled_data_YYYY-MM-DD-HH_upload_YYYY-MM-DD-HH-MM.csv")
    print(f"\n💡 Date format:")
    print(f"   YYYY-MM-DD HH:MM:00 (no seconds, no microseconds)")

if __name__ == "__main__":
    test_new_date_format()
