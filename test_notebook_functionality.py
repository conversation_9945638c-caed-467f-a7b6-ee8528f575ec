#!/usr/bin/env python3
"""
Test script to verify the S3 GRIB processor notebook functionality
This script runs the key tests from the Jupyter notebook
"""

print("🌟 S3 GRIB Processor - Notebook Functionality Test")
print("=" * 60)

try:
    # Import the S3 GRIB processor
    from grib_processor_S3 import (
        list_s3_objects,
        get_s3_files_for_run_date,
        process_and_export_s3_grib_files,
        bucket_name
    )
    import pandas as pd
    import os
    from datetime import datetime

    print("✅ Imports successful!")
    print(f"📦 S3 Bucket: {bucket_name}")

    # Test 1: S3 Connectivity
    print("\n🔗 Test 1: S3 Connectivity")
    print("-" * 30)
    
    objects = list_s3_objects()
    
    if objects:
        print(f"✅ S3 connectivity successful!")
        print(f"📊 Found {len(objects)} objects in bucket")
        print("\n📋 First 5 objects:")
        for i, obj in enumerate(objects[:5]):
            print(f"   {i+1}. {obj}")
    else:
        print("❌ No objects found in S3 bucket")

    # Test 2: File Discovery
    print("\n📅 Test 2: File Discovery")
    print("-" * 30)
    
    test_run_date = "2025-04-28-00"
    print(f"🔍 Searching for files with run date: {test_run_date}")
    
    matching_files = get_s3_files_for_run_date(test_run_date)
    
    if matching_files:
        print(f"✅ Found {len(matching_files)} matching files")
        print("\n📋 First 3 files:")
        for i, file_key in enumerate(matching_files[:3]):
            print(f"   {i+1}. {file_key}")
        if len(matching_files) > 3:
            print(f"   ... and {len(matching_files) - 3} more files")
    else:
        print(f"❌ No files found for run date: {test_run_date}")

    # Test 3: Complete Processing Pipeline
    if matching_files:
        print("\n🚀 Test 3: Complete Processing Pipeline")
        print("-" * 40)
        
        print(f"🚀 Testing complete S3 processing pipeline for: {test_run_date}")
        
        start_time = datetime.now()
        
        # Process with enhanced features
        enhanced_df, csv_file = process_and_export_s3_grib_files(
            run_date=test_run_date,
            include_enhanced_columns=True
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        if not enhanced_df.empty and csv_file:
            print(f"\n✅ Processing completed in {processing_time:.1f} seconds")
            print(f"📊 Data shape: {enhanced_df.shape}")
            print(f"📤 CSV exported to: {csv_file}")
            print(f"📊 File size: {os.path.getsize(csv_file)/1024/1024:.1f} MB")
            
            # Show enhanced columns
            print(f"\n📋 Enhanced columns: {list(enhanced_df.columns)}")
            
            # Show sample data
            print(f"\n📋 Sample data (first 3 rows):")
            sample_cols = ['latitude', 'longitude', 'valid_time_fr', 'precipitation', 'run_month', 'run_day']
            available_cols = [col for col in sample_cols if col in enhanced_df.columns]
            print(enhanced_df[available_cols].head(3))
            
            print(f"\n🎉 S3 GRIB processing successful!")
        else:
            print("❌ Processing failed")
    else:
        print("\n⚠️ Test 3: Skipped (no files available)")

    # Test 4: Quick Data Analysis
    if 'enhanced_df' in locals() and not enhanced_df.empty:
        print("\n📊 Test 4: Quick Data Analysis")
        print("-" * 30)
        
        # Basic statistics
        print(f"Total rows: {len(enhanced_df):,}")
        print(f"Total files: {enhanced_df['file_name'].nunique()}")
        print(f"Geographic coverage:")
        print(f"  Latitude: {enhanced_df['latitude'].min():.1f} to {enhanced_df['latitude'].max():.1f}")
        print(f"  Longitude: {enhanced_df['longitude'].min():.1f} to {enhanced_df['longitude'].max():.1f}")
        
        # Precipitation analysis
        if 'precipitation' in enhanced_df.columns:
            precip_stats = enhanced_df['precipitation'].describe()
            print(f"\nPrecipitation (mm):")
            print(f"  Min: {precip_stats['min']:.1f}")
            print(f"  Max: {precip_stats['max']:.1f}")
            print(f"  Mean: {precip_stats['mean']:.2f}")
            print(f"  Non-zero values: {(enhanced_df['precipitation'] > 0).sum():,}")
        
        # S3 metadata
        if 'min_date_upload' in enhanced_df.columns:
            print(f"\nS3 Upload metadata:")
            print(f"  Upload range: {enhanced_df['min_date_upload'].iloc[0]} to {enhanced_df['max_date_upload'].iloc[0]}")
        
        print(f"\n✅ Data analysis completed!")
    else:
        print("\n⚠️ Test 4: Skipped (no data available)")

    # Test Summary
    print("\n📝 Test Summary")
    print("=" * 20)
    
    tests = {
        "S3 Connectivity": 'objects' in locals() and len(objects) > 0,
        "File Discovery": 'matching_files' in locals() and len(matching_files) > 0,
        "Data Processing": 'enhanced_df' in locals() and not enhanced_df.empty,
        "CSV Export": 'csv_file' in locals() and csv_file and os.path.exists(csv_file)
    }
    
    passed = sum(tests.values())
    total = len(tests)
    
    print(f"🎯 Results: {passed}/{total} tests passed")
    print()
    
    for test_name, result in tests.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name:<20} {status}")
    
    print()
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("🚀 S3 GRIB processor notebook functionality is working perfectly!")
        print("\n📚 You can now use the Jupyter notebook: s3_grib_test.ipynb")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")

except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("Test completed!")
