#!/usr/bin/env python3
"""
Simple test script to verify S3 connection and list objects
"""

import boto3
import sys

# S3 Configuration
aws_access_key_id = "********************"
aws_secret_access_key = "MaGzISt+VYu2SXnxCbczRxrsOylCWAzsDbhuWJNf"
region_name = 'eu-west-3'
bucket_name = 'ecmwf-forecast-data-source'

print("🔄 Testing S3 connection...")
print(f"Bucket: {bucket_name}")
print(f"Region: {region_name}")

try:
    # Initialize S3 client
    s3_client = boto3.client(
        's3',
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=region_name
    )
    
    print("✅ S3 client initialized successfully")
    
    # Test bucket access
    print("\n🔄 Testing bucket access...")
    response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=10)
    
    if 'Contents' in response:
        objects = response['Contents']
        print(f"✅ Successfully accessed bucket! Found {len(objects)} objects (showing first 10)")
        
        print("\n📋 First 10 objects:")
        for i, obj in enumerate(objects):
            print(f"   {i+1}. {obj['Key']} (size: {obj['Size']} bytes, modified: {obj['LastModified']})")
            
        # Test if objects look like GRIB files
        grib_like = [obj['Key'] for obj in objects if len(obj['Key']) >= 15]
        print(f"\n📊 Objects that could be GRIB files: {len(grib_like)}")
        
    else:
        print("⚠️ Bucket is empty or no objects found")
        
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)

print("\n🎉 S3 connection test completed successfully!")
